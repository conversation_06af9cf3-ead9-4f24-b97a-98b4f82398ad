import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import type { Database } from '../../../../lib/database.types';

interface AssignmentProgressRequest {
  assignmentId: string;
  completed: boolean;
  timeSpent: number; // in seconds
  score: number; // percentage
  metadata?: {
    matches: number;
    attempts: number;
    gameType: string;
  };
}

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            );
          },
        },
      }
    );
    
    // Verify authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body: AssignmentProgressRequest = await request.json();
    const { assignmentId, completed, timeSpent, score, metadata } = body;

    // Verify the assignment exists and user has access
    const { data: assignment, error: assignmentError } = await supabase
      .from('assignments')
      .select('id, class_id, created_by, vocabulary_assignment_list_id')
      .eq('id', assignmentId)
      .single();

    if (assignmentError || !assignment) {
      return NextResponse.json(
        { error: 'Assignment not found' },
        { status: 404 }
      );
    }

    // Check if user is a student in the class
    const { data: enrollment } = await supabase
      .from('class_enrollments')
      .select('student_id')
      .eq('class_id', assignment.class_id)
      .eq('student_id', user.id)
      .single();
    
    if (!enrollment) {
      return NextResponse.json(
        { error: 'Access denied - not enrolled in this class' },
        { status: 403 }
      );
    }

    // Update or create assignment progress
    const progressData = {
      assignment_id: assignmentId,
      student_id: user.id,
      status: completed ? 'completed' : 'in_progress',
      score: score,
      accuracy: score, // For memory game, score is accuracy
      attempts: metadata?.attempts || 1,
      time_spent: timeSpent,
      updated_at: new Date().toISOString(),
      ...(completed && { completed_at: new Date().toISOString() })
    };

    const { data: updatedProgress, error: progressError } = await supabase
      .from('assignment_progress')
      .upsert([progressData])
      .select()
      .single();

    if (progressError) {
      console.error('Error updating progress:', progressError);
      return NextResponse.json(
        { error: 'Failed to update progress' },
        { status: 500 }
      );
    }

    // Create game session record
    if (metadata) {
      const gameSessionData = {
        student_id: user.id,
        assignment_id: assignmentId,
        game_type: metadata.gameType,
        session_data: {
          matches: metadata.matches,
          attempts: metadata.attempts,
          timeSpent: timeSpent
        },
        score: score,
        accuracy: score,
        time_spent_seconds: timeSpent,
        vocabulary_practiced: [], // Memory game doesn't track individual words yet
        completed_at: completed ? new Date().toISOString() : null
      };

      const { error: sessionError } = await supabase
        .from('assignment_game_sessions')
        .insert([gameSessionData]);

      if (sessionError) {
        console.error('Error creating game session:', sessionError);
      }
    }

    return NextResponse.json({
      success: true,
      progress: updatedProgress
    });

  } catch (error) {
    console.error('Assignment progress error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/* Word Blast Game Styles */

@keyframes rocketTrail {
  0% {
    opacity: 0.8;
    height: 10px;
  }
  100% {
    opacity: 0;
    height: 40px;
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Specific component styles */
.rocket-trail {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  z-index: -1;
  background: linear-gradient(to top, transparent, rgba(255, 165, 0, 0.3), rgba(255, 69, 0, 0.7));
  border-radius: 2px;
  animation: rocketTrail 0.3s linear infinite;
}

.game-countdown {
  animation: pulse 1s ease-in-out infinite;
}

.powerup-badge {
  position: relative;
  overflow: hidden;
}

.powerup-badge::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.powerup-icon {
  animation: bounce 2s ease infinite;
}

.success-text {
  background: linear-gradient(90deg, #4ade80, #22d3ee);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.active-word {
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* Game environment enhancements */
.star-field {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
}

.star {
  position: absolute;
  background-color: white;
  border-radius: 50%;
  opacity: 0.4;
}

.option-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.option-button:active {
  transform: translateY(0);
}

/* Mobile responsiveness adjustments */
@media (max-width: 640px) {
  .game-controls {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .powerup-container {
    justify-content: center;
  }
} 
/* Base Game Styles */
.tycoon-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f9f7f1;
  color: #333;
  font-family: 'N<PERSON><PERSON>', 'Segoe UI', sans-serif;
}

.tycoon-header {
  background-color: #14532d;
  color: #ffffff;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 10;
}

.tycoon-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1rem;
}

.tycoon-footer {
  background-color: #14532d;
  color: #ffffff;
  padding: 0.5rem;
  text-align: center;
  font-size: 0.8rem;
}

/* Currency & Stats Display */
.currency-display {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.25rem;
  font-weight: bold;
  color: #eab308;
}

.stats-bar {
  display: flex;
  justify-content: space-between;
  background-color: #e0f2f1;
  padding: 0.75rem;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  position: sticky;
  top: 3.5rem;
  z-index: 9;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-weight: bold;
  font-size: 1.1rem;
  color: #059669;
}

.stat-label {
  font-size: 0.8rem;
  color: #3f3f46;
}

/* Game Board */
.game-board {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin: 1rem 0;
}

.translation-challenge {
  background-color: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  text-align: center;
}

.challenge-word {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 1rem;
  color: #1e293b;
}

.translation-input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 1.1rem;
  margin-bottom: 1rem;
  transition: border-color 0.2s;
}

.translation-input:focus {
  border-color: #059669;
  outline: none;
  box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.2);
}

.submit-btn {
  background-color: #059669;
  color: white;
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
}

.submit-btn:hover {
  background-color: #047857;
}

.submit-btn:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

/* City View */
.city-view {
  position: relative;
  height: 400px;
  background-image: url('/games/translation-tycoon/backgrounds/city-day.png');
  background-size: cover;
  background-position: bottom;
  border-radius: 0.75rem;
  margin-bottom: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.building {
  position: absolute;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: bottom;
  transition: all 0.3s ease;
  cursor: pointer;
  z-index: 1;
}

.building:hover {
  transform: translateY(-10px) scale(1.05);
  filter: drop-shadow(0 4px 8px rgba(0,0,0,0.2));
}

.building-level {
  position: absolute;
  bottom: -25px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #059669;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: bold;
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Shop */
.shop-container {
  background-color: white;
  border-radius: 0.75rem;
  padding: 1rem;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.shop-tabs {
  display: flex;
  margin-bottom: 1rem;
  border-bottom: 2px solid #e5e7eb;
}

.shop-tab {
  padding: 0.75rem 1rem;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  margin-bottom: -2px;
  font-weight: 600;
}

.shop-tab.active {
  border-bottom-color: #059669;
  color: #059669;
}

.shop-items {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.shop-item {
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 0.2s, box-shadow 0.2s;
}

.shop-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.shop-item-img {
  width: 64px;
  height: 64px;
  margin-bottom: 0.5rem;
}

.shop-item-name {
  font-weight: bold;
  margin-bottom: 0.25rem;
}

.shop-item-desc {
  font-size: 0.8rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
}

.shop-item-price {
  font-weight: bold;
  color: #eab308;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.buy-btn {
  background-color: #059669;
  color: white;
  border: none;
  border-radius: 0.25rem;
  padding: 0.5rem 0.75rem;
  font-size: 0.8rem;
  cursor: pointer;
  transition: background-color 0.2s;
  width: 100%;
}

.buy-btn:hover {
  background-color: #047857;
}

.buy-btn:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

/* Challenge Word Bonus */
.challenge-word-marker {
  display: inline-block;
  margin-left: 0.5rem;
  padding: 0.25rem 0.5rem;
  background-color: #f97316;
  color: white;
  border-radius: 0.25rem;
  font-size: 0.7rem;
  font-weight: bold;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

/* Animations */
.coin-animation {
  position: fixed;
  font-weight: bold;
  color: #eab308;
  animation: float-up 1s forwards;
  pointer-events: none;
}

@keyframes float-up {
  0% { transform: translateY(0); opacity: 1; }
  100% { transform: translateY(-50px); opacity: 0; }
}

/* Feedback Message */
.feedback-message {
  position: fixed;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  padding: 0.75rem 1.5rem;
  border-radius: 0 0 0.5rem 0.5rem;
  color: white;
  font-weight: bold;
  z-index: 100;
  animation: slideDown 0.3s ease-out forwards;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.feedback-message.success {
  background-color: #059669;
}

.feedback-message.error {
  background-color: #dc2626;
}

@keyframes slideDown {
  0% { transform: translate(-50%, -100%); }
  100% { transform: translate(-50%, 0); }
}

/* Desktop optimizations */
@media (min-width: 768px) {
  .tycoon-main {
    padding: 1.5rem;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
  }

  .game-board {
    margin: 0;
  }

  .shop-container {
    height: 100%;
  }
}

/* Enhanced desktop styles */
@media (min-width: 1024px) {
  .tycoon-container {
    background-color: #f0f9ff;
    background-image: linear-gradient(to bottom, #dcfce7, #f0f9ff);
    min-height: 100vh;
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .tycoon-main {
    padding: 0.5rem 1rem;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .stats-bar {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    background-color: rgba(224, 242, 241, 0.9);
    backdrop-filter: blur(5px);
  }

  .city-view {
    height: calc(100vh - 12rem);
    min-height: 400px;
    max-height: 800px;
    transition: height 0.3s ease;
    border-radius: 0.75rem;
    box-shadow: 0 8px 20px rgba(0,0,0,0.15);
    flex: 1;
  }

  .translation-challenge {
    position: absolute;
    bottom: 1.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    max-width: 800px;
    z-index: 5;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(5px);
    padding: 1rem 1.5rem;
    border: 2px solid #059669;
  }

  .building {
    transform: scale(1.2);
  }

  .building:hover {
    transform: translateY(-15px) scale(1.25);
  }

  /* Full-screen layout for the playing state */
  .desktop-layout {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .desktop-layout .city-view-container {
    flex: 1;
    position: relative;
    height: 100%;
    width: 100%;
    overflow: hidden;
  }

  .desktop-layout .shop-sidebar {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 320px;
    max-height: calc(100% - 2rem);
    overflow-y: auto;
    z-index: 5;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(5px);
    border-radius: 0.75rem;
    border: 2px solid #059669;
  }

  .tycoon-footer {
    padding: 0.25rem;
    font-size: 0.7rem;
  }

  /* Compact Shop styles */
  .shop-scroll-container {
    max-height: calc(100vh - 15rem);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding-right: 0.5rem;
  }

  .shop-scroll-container::-webkit-scrollbar {
    width: 6px;
  }

  .shop-scroll-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
  }

  .shop-scroll-container::-webkit-scrollbar-thumb {
    background: #059669;
    border-radius: 10px;
  }

  .shop-container {
    padding: 0.75rem;
  }
  
  .shop-tabs {
    margin-bottom: 0.5rem;
  }
  
  .shop-tab {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
  }

  .compact-shop-item {
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 0.5rem;
    transition: transform 0.2s, box-shadow 0.2s;
  }

  .compact-shop-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    border-color: #059669;
  }

  .compact-shop-img {
    width: 40px;
    height: 40px;
    object-fit: contain;
  }

  .compact-shop-name {
    font-weight: bold;
    font-size: 0.9rem;
    color: #059669;
    margin-bottom: 0.1rem;
  }

  .compact-shop-desc {
    font-size: 0.75rem;
    color: #6b7280;
    line-height: 1.2;
  }

  .compact-shop-price {
    font-weight: bold;
    color: #eab308;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.9rem;
  }

  .compact-buy-btn {
    background-color: #059669;
    color: white;
    border: none;
    border-radius: 0.25rem;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .compact-buy-btn:hover {
    background-color: #047857;
  }

  .compact-buy-btn:disabled {
    background-color: #9ca3af;
    cursor: not-allowed;
  }

  /* Challenge Word Input */
  .translation-input {
    padding: 0.5rem;
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }
  
  .submit-btn {
    padding: 0.5rem 1rem;
  }
  
  .challenge-word {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
  }
  
  /* Animation for page transitions */
  .page-transition {
    animation: fadeIn 0.5s ease-in-out;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
}

/* Header Shop Dropdown Styles */
.shop-dropdown-wrapper {
  position: relative; /* Anchor point for the absolute dropdown */
}

.header-shop-dropdown {
  position: absolute;
  top: calc(100% + 0.5rem); /* Position below the header */
  right: 1rem; /* Align to the right side */
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 8px 16px rgba(0,0,0,0.15);
  padding: 1rem;
  z-index: 20; /* Ensure it's above other header content */
  width: 350px; /* Fixed width for the dropdown */
  max-height: 70vh; /* Limit height */
  display: flex;
  flex-direction: column;
}

.header-shop-dropdown .shop-tabs {
  /* Adjust tab styles if needed for dropdown */
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 0.5rem;
  flex-shrink: 0; /* Prevent tabs from shrinking */
}

.header-shop-dropdown .shop-tab {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
}

.header-shop-dropdown .shop-scroll-container {
  overflow-y: auto; /* Enable scrolling for items */
  padding-right: 0.5rem; /* Space for scrollbar */
  flex-grow: 1; /* Allow container to take available space */
}

/* Rename compact shop item styles to header shop item styles */
.header-shop-item {
  display: flex;
  flex-direction: column;
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  margin-bottom: 0.75rem;
  background-color: #f9fafb; /* Light background for items */
  transition: box-shadow 0.2s ease-in-out;
}

.header-shop-item:hover {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-shop-img {
  width: 48px; /* Slightly smaller image for dropdown */
  height: 48px;
  object-fit: contain;
  margin-right: 0.5rem; /* Ensure margin is applied correctly */
}

.header-shop-name {
  font-weight: 600;
  font-size: 0.9rem;
  color: #1f2937;
  margin-bottom: 0.1rem;
}

.header-shop-desc {
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
  line-height: 1.3;
}

.header-shop-price {
  font-weight: 600;
  color: #b45309; /* Amber color for price */
  font-size: 0.9rem;
  display: flex;
  align-items: center;
}

.header-buy-btn {
  background-color: #059669;
  color: white;
  border: none;
  border-radius: 0.375rem; /* Tailwind's rounded-md */
  padding: 0.3rem 0.6rem;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.header-buy-btn:hover {
  background-color: #047857;
}

.header-buy-btn:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
  opacity: 0.7;
}

/* Custom scrollbar for the shop dropdown */
.header-shop-dropdown .shop-scroll-container::-webkit-scrollbar {
  width: 8px;
}

.header-shop-dropdown .shop-scroll-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.header-shop-dropdown .shop-scroll-container::-webkit-scrollbar-thumb {
  background: #a3a3a3;
  border-radius: 10px;
}

.header-shop-dropdown .shop-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #8a8a8a;
} 
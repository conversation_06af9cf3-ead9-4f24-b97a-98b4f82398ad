/* 3D card flip animation */
.transform-style-3d {
  transform-style: preserve-3d;
}

.backface-hidden {
  backface-visibility: hidden;
}

.rotate-y-180 {
  transform: rotateY(180deg);
}

/* Aspect ratio utilities */
.aspect-w-3 {
  position: relative;
  padding-bottom: calc(var(--tw-aspect-h) / var(--tw-aspect-w) * 100%);
  --tw-aspect-w: 3;
}

.aspect-h-4 {
  --tw-aspect-h: 4;
}

.aspect-w-3 > * {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

/* Transition utilities */
.transition-transform {
  transition-property: transform;
}

.duration-300 {
  transition-duration: 300ms;
}

.duration-500 {
  transition-duration: 500ms;
}

/* Transform utilities */
.transform-gpu {
  transform: translate3d(var(--tw-translate-x), var(--tw-translate-y), 0) rotateX(var(--tw-rotate-x)) rotateY(var(--tw-rotate-y)) rotateZ(var(--tw-rotate-z)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

/* Card hover effects */
.card-hover-effect {
  transition: all 0.3s ease;
}

.card-hover-effect:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Matched card pulse animation */
@keyframes pulse-success {
  0% {
    box-shadow: 0 0 0 0 rgba(52, 211, 153, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(52, 211, 153, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(52, 211, 153, 0);
  }
}

.pulse-success {
  animation: pulse-success 1.5s ease infinite;
}

/* Shake animation for mismatched cards */
@keyframes shake {
  0% { transform: translateX(0); }
  10% { transform: translateX(-5px) rotateY(180deg); }
  20% { transform: translateX(5px) rotateY(180deg); }
  30% { transform: translateX(-5px) rotateY(180deg); }
  40% { transform: translateX(5px) rotateY(180deg); }
  50% { transform: translateX(-5px) rotateY(180deg); }
  60% { transform: translateX(5px) rotateY(180deg); }
  70% { transform: translateX(-5px) rotateY(180deg); }
  80% { transform: translateX(5px) rotateY(180deg); }
  90% { transform: translateX(-5px) rotateY(180deg); }
  100% { transform: translateX(0) rotateY(180deg); }
}

.shake {
  animation: shake 0.8s ease-in-out;
}

/* Celebration confetti animation */
@keyframes float-up {
  0% {
    transform: translateY(0) rotate(0);
    opacity: 1;
  }
  80% {
    opacity: 0.7;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

.float-up {
  animation: float-up 3s ease-out forwards;
}

/* Card theme variations */
.card-theme-animals {
  background: linear-gradient(135deg, #10b981, #047857);
}

.card-theme-colors {
  background: linear-gradient(135deg, #8b5cf6, #6d28d9);
}

.card-theme-food {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.card-theme-countries {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

/* General container */
.game-container {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  transition: background-image 0.5s ease;
}

/* Game board */
.game-board {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 1rem;
  padding: 1.5rem;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Game stats */
.game-stats {
  display: flex;
  justify-content: space-between;
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

/* Cards Grid */
.cards-grid {
  display: grid;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  gap: 0.75rem;
}

/* Responsive grid to dynamically adjust to number of cards */
@media (max-width: 480px) {
  .memory-card {
    aspect-ratio: 2/3;
  }
  
  .memory-card-front,
  .memory-card-back {
    padding: 0.25rem;
  }
  
  .memory-card-front {
    font-size: 1.5rem;
  }
  
  .memory-card-back span {
    font-size: 0.875rem;
  }
}

@media (min-width: 768px) {
  .cards-grid {
    gap: 1rem;
  }
}

@media (min-width: 1024px) {
  .cards-grid {
    gap: 1.25rem;
  }
}

/* Memory Card styles */
.memory-card {
  aspect-ratio: 3/4;
  perspective: 1000px;
  cursor: pointer;
  border-radius: 0.5rem;
  overflow: hidden;
  transform-style: preserve-3d;
  position: relative;
  transition: transform 0.2s ease;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.memory-card:hover {
  transform: translateY(-5px);
}

.memory-card-inner {
  width: 100%;
  height: 100%;
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-radius: 0.5rem;
}

.memory-card.flipped .memory-card-inner {
  transform: rotateY(180deg);
}

.memory-card-front,
.memory-card-back {
  width: 100%;
  height: 100%;
  position: absolute;
  backface-visibility: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  padding: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  font-weight: 500;
  text-align: center;
}

.memory-card-front {
  color: white;
  font-size: 2rem; 
}

.memory-card-back {
  transform: rotateY(180deg);
  word-break: break-word;
  hyphens: auto;
}

/* Match animation */
.memory-card.matched .memory-card-inner {
  animation: matched 0.6s ease forwards;
  box-shadow: 0 0 15px rgba(52, 211, 153, 0.7);
}

@keyframes matched {
  0% {
    transform: rotateY(180deg) scale(1);
  }
  50% {
    transform: rotateY(180deg) scale(1.15);
  }
  100% {
    transform: rotateY(180deg) scale(1);
  }
}

/* Mismatch animation */
.memory-card.mismatch .memory-card-inner {
  animation: shake 0.5s ease;
}

@keyframes shake {
  0%, 100% { transform: rotateY(180deg) translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: rotateY(180deg) translateX(-5px); }
  20%, 40%, 60%, 80% { transform: rotateY(180deg) translateX(5px); }
}

/* Card image styles */
.card-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 0.25rem;
}

/* Game controls */
.game-controls {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

/* Win screen */
.win-screen {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 10;
  animation: fadeIn 0.5s;
  border-radius: 0.5rem;
}

.win-container {
  text-align: center;
  padding: 2rem;
  background-color: white;
  border-radius: 1rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  max-width: 90%;
  width: 400px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Settings container */
.settings-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 1rem;
}

.settings-card {
  width: 100%;
  max-width: 600px;
  background-color: white;
  border-radius: 1rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

/* Theme selection modal */
.theme-selector {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.theme-item {
  border: 2px solid transparent;
  border-radius: 0.5rem;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s;
}

.theme-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.theme-item.selected {
  border-color: #4f46e5;
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
}

.theme-preview {
  height: 100px;
  background-size: cover;
  background-position: center;
}

.theme-name {
  padding: 0.5rem;
  text-align: center;
  font-weight: 500;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  z-index: 50;
}

.modal-content {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

/* Theme-related styles */
body.themed {
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  transition: background-image 0.5s ease;
}

/* Image card styles */
.memory-card.image-card .memory-card-back {
  padding: 0.25rem;
}

/* Fullscreen mode */
.fullscreen .game-container {
  height: 100vh;
  padding: 0.5rem;
}

.fullscreen .cards-grid {
  max-width: 100%;
  flex-grow: 1;
}

.fullscreen .memory-card {
  max-height: none;
} 
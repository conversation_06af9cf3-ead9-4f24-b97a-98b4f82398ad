<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <title>Memory Match - Spanish Learning</title>
</head>
<body>
    <!-- Navigation will be injected here -->
    <script type="module">
        import { injectNavigation } from './../components/navigation.js';
        injectNavigation();
    </script>

    <div class="game-wrapper">
        <header class="header">
            <div class="header-content">
                <div class="left-controls">
                    <a href="../index.html" class="nav-btn"><i class="fas fa-home"></i></a>
                    <button id="customWordsBtn" class="nav-btn"><i class="fas fa-plus"></i> Custom Words</button>
                    <button id="themeBtn" class="nav-btn"><i class="fas fa-palette"></i> Theme</button>
                </div>

                <h1 class="title">Memory Match</h1>

                <div class="right-controls">
                    <div class="stats-group">
                        <div class="stat-item">
                            <i class="fas fa-star"></i>
                            <span>Matches: <span id="matchCount">0</span></span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-redo"></i>
                            <span>Attempts: <span id="attempts">0</span></span>
                        </div>
                    </div>
                    <button id="resetGame" class="nav-btn"><i class="fas fa-sync"></i></button>
                    <button id="fullscreenBtn" class="nav-btn"><i class="fas fa-expand"></i></button>
                </div>
            </div>
        </header>

        <!-- Game Board -->
        <div class="game-container">
            <div class="cards-container">
                <div class="cards-grid" id="cardsGrid"></div>
            </div>
        </div>

        <!-- Custom Words Modal -->
        <div class="modal" id="customWordsModal">
            <div class="modal-content">
                <h2 class="modal-title">Create Your Word Pairs</h2>
                <div class="word-pair-inputs">
                    <select id="pairType" class="select-style">
                        <option value="word">Match words</option>
                        <option value="image">Match word to Image</option>
                    </select>
                    <div class="input-group">
                        <textarea id="vocabInput" placeholder="Enter vocab pairs (one per line)" class="textarea-style"></textarea>
                        <div class="word-entry">
                            <input type="text" id="spanishWord" placeholder="Enter word" class="input-style">
                            <button id="imageSearchTrigger" class="image-search-btn"><i class="fas fa-image"></i></button>
                            <button id="uploadImageBtn" class="image-upload-btn"><i class="fas fa-upload"></i></button>
                            <input type="file" id="customImageInput" accept="image/*" style="display: none;">
                        </div>
                    </div>
                    <button id="addWordPair" class="btn btn-primary">Add Pair</button>
                </div>
                <div class="word-pairs-list" id="wordPairsList"></div>
                <div class="modal-buttons">
                    <button id="closeCustomWordsBtn" class="btn btn-secondary">Close</button>
                    <button id="startGame" class="btn btn-primary" disabled>Start Game</button>
                </div>
            </div>
        </div>

        <!-- Image Search Modal -->
        <div class="modal" id="imageSearchModal">
            <div class="modal-content">
                <h3>Search Images</h3>
                <div class="search-controls">
                    <input type="text" id="imageSearchInput" placeholder="Search for images...">
                    <button class="btn btn-primary" id="searchImagesBtn">
                        <i class="fas fa-search"></i> Search
                    </button>
                </div>
                <div class="image-results" id="imageSearchResults"></div>
                <div id="imageSearchLoading" class="loading-spinner" style="display: none;">
                    Searching...
                </div>
                <div class="modal-buttons">
                    <button class="btn btn-secondary" id="closeImageSearch">Close</button>
                </div>
            </div>
        </div>

        <!-- Win Modal -->
        <div class="modal" id="winModal">
            <div class="modal-content">
                <h2 class="modal-title">Congratulations! 🎉</h2>
                <div class="win-stats">
                    <div class="stat-item">
                        <i class="fas fa-star"></i>
                        <span>Matches: <span id="finalMatches">0</span></span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-redo"></i>
                        <span>Attempts: <span id="finalAttempts">0</span></span>
                    </div>
                </div>
                <button id="playAgainBtn" class="btn btn-primary">Play Again</button>
            </div>
        </div>

        <!-- Modal Overlay -->
        <div class="modal-overlay"></div>

        <!-- Theme Modal -->
        <div class="modal" id="themeModal">
            <div class="modal-content">
                <h2 class="modal-title">Select Theme</h2>
                <div class="theme-grid" id="themeGrid">
                    <!-- Theme options will be populated by JavaScript -->
                </div>
                <div class="theme-upload">
                    <button id="uploadThemeBtn" class="btn btn-secondary">
                        <i class="fas fa-upload"></i> Upload Custom Background
                    </button>
                    <input type="file" id="themeImageInput" accept="image/*" style="display: none;">
                </div>
                <div class="modal-buttons">
                    <button id="closeThemeBtn" class="btn btn-secondary">Close</button>
                </div>
            </div>
        </div>

        <!-- Audio Elements -->
        <audio id="correctSound" src="sounds/correct.mp3"></audio>
        <audio id="wrongSound" src="wrong.mp3"></audio>
        <audio id="winSound" src="win.mp3"></audio>

        <script src="script.js"></script>
    </div>

</body>
</html>

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #1a2a6c, #b21f1f, #fdbb2d);
    color: #333;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    animation: gradientBG 15s ease infinite;
    background-size: 400% 400%;
}

@keyframes gradientBG {
    0% { background-position: 0% 50% }
    50% { background-position: 100% 50% }
    100% { background-position: 0% 50% }
}

.game-wrapper {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    width: 95%;
    max-width: 1200px;
    backdrop-filter: blur(10px);
    transform: translateY(0);
    transition: transform 0.3s ease;
    position: relative;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.game-wrapper:hover {
    transform: translateY(-5px);
}

.header {
    text-align: center;
    margin-bottom: 30px;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    padding: 10px 0;
}

.left-controls,
.right-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.stats-group {
    display: flex;
    gap: 15px;
    margin-right: 10px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1.2rem;
    background: rgba(0, 0, 0, 0.8);
    padding: 8px 15px;
    border-radius: 8px;
    font-weight: 600;
}

.stat-item i {
    color: #4a90e2;
}

.stat-item span {
    color: white;
}

.title {
    font-size: 2.8rem;
    color: white;
    text-shadow: 
        -2px -2px 0 #000,
        2px -2px 0 #000,
        -2px 2px 0 #000,
        2px 2px 0 #000;
    margin: 0;
    text-align: center;
    font-weight: 700;
}

.nav-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    background: linear-gradient(135deg, #4a90e2, #7e57c2);
    color: white;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
}

.nav-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.game-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: calc(100vh - 150px);
    min-height: 300px;
    position: relative;
}

.cards-container {
    width: 100%;
    height: 100%;
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.cards-grid {
    display: grid;
    gap: 15px;
    width: 100%;
    height: 100%;
    max-width: min(100%, calc(100vh - 200px) * 2);
    margin: 0 auto;
}

.matched {
    background: linear-gradient(135deg, #86efac, #d4f8db);
    border-color: #86efac;
    color: #065f46;
    animation: matchPulse 0.5s ease-out;
}

@keyframes matchPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.btn {
    padding: 12px 25px;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
}

.btn-primary {
    background: linear-gradient(135deg, #1a2a6c, #b21f1f);
    color: white;
}

.btn-secondary {
    background: #f0f0f0;
    color: #333;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    display: none;
    z-index: 1000;
}

.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    z-index: 1001;
    display: none;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-title {
    font-size: 1.8rem;
    color: #1a2a6c;
    margin-bottom: 20px;
    text-align: center;
}

.word-pair-inputs {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.input-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.input-style,
.select-style,
.textarea-style {
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    width: 100%;
    transition: all 0.3s ease;
}

.input-style:focus,
.select-style:focus,
.textarea-style:focus {
    outline: none;
    border-color: #4a90e2;
    box-shadow: 0 0 10px rgba(74, 144, 226, 0.2);
}

.textarea-style {
    height: 100px;
    resize: vertical;
}

.word-pairs-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
    margin: 20px 0;
}

.word-pair {
    background: rgba(74, 144, 226, 0.1);
    padding: 10px;
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.word-pair button {
    background: none;
    border: none;
    color: #b21f1f;
    cursor: pointer;
    font-size: 1.2rem;
    padding: 5px;
    transition: all 0.3s ease;
}

.word-pair button:hover {
    transform: scale(1.2);
}

.modal-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.win-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin: 20px 0;
}

/* Fullscreen styles */
.fullscreen .game-wrapper {
    width: 100vw;
    height: 100vh;
    max-width: none;
    margin: 0;
    border-radius: 0;
    padding: 20px;
    display: flex;
    flex-direction: column;
}

.fullscreen .game-container {
    flex: 1;
    height: calc(100vh - 100px);
}

.fullscreen .cards-grid {
    max-width: min(calc(100vh - 150px) * 2, 100%);
}

/* Responsive styles */
@media (max-width: 768px) {
    .header-content {
        flex-wrap: wrap;
    }

    .title {
        font-size: 1.8rem;
        order: -1;
        width: 100%;
        text-align: center;
    }

    .left-controls,
    .right-controls {
        flex: 1;
    }

    .left-controls {
        justify-content: flex-start;
    }

    .right-controls {
        justify-content: flex-end;
    }

    .stats-group {
        display: flex;
        gap: 10px;
    }

    .stat-item {
        font-size: 0.9rem;
    }

    .game-container {
        height: calc(100vh - 200px);
    }
}

/* Add resize observer styles */
.cards-grid.resizing {
    transition: none !important;
}

.word-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.word-content img {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;
}

.delete-btn {
    color: #dc3545;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1.2rem;
    opacity: 0.7;
    transition: all 0.2s ease;
    padding: 4px 8px;
    border-radius: 4px;
}

.delete-btn:hover {
    opacity: 1;
    background: #fee2e2;
}

.card {
    aspect-ratio: 1;
    perspective: 1000px;
    cursor: pointer;
    width: 100%;
    height: 100%;
    min-width: 80px;
    min-height: 80px;
    transform-style: preserve-3d;
    transition: transform 0.6s cubic-bezier(0.4, 0.0, 0.2, 1);
    position: relative;
}

.card.flipped {
    transform: rotateY(180deg);
}

.card-front, .card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card:hover .card-front,
.card:hover .card-back {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.card-front {
    background: linear-gradient(135deg, #4a90e2, #7e57c2);
    color: white;
    font-size: 3rem;
    font-weight: bold;
    transform: rotateY(0deg);
}

.card-back {
    background: white;
    transform: rotateY(180deg);
    border: 2px solid #4a90e2;
    font-size: 1.5rem;
    word-break: break-word;
    text-align: center;
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

.card-back img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 8px;
}

/* Theme Modal Styles */
.theme-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
    max-height: 400px;
    overflow-y: auto;
    padding: 10px;
}

.theme-option {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    cursor: pointer;
    aspect-ratio: 16/9;
    transition: transform 0.3s ease;
}

.theme-option:hover {
    transform: scale(1.05);
}

.theme-option img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.theme-option.selected {
    box-shadow: 0 0 0 3px #4a90e2;
}

.theme-option .theme-name {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px;
    font-size: 0.9rem;
    text-align: center;
}

.theme-upload {
    margin: 20px 0;
    text-align: center;
}

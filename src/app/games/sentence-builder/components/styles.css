/* Animations for correct/incorrect feedback */
@keyframes celebrate {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

@keyframes shake {
  10%, 90% { transform: translate3d(-1px, 0, 0); }
  20%, 80% { transform: translate3d(2px, 0, 0); }
  30%, 50%, 70% { transform: translate3d(-3px, 0, 0); }
  40%, 60% { transform: translate3d(3px, 0, 0); }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-20px); }
  60% { transform: translateY(-10px); }
}

/* Dragging animations */
@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(79, 70, 229, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(79, 70, 229, 0); }
  100% { box-shadow: 0 0 0 0 rgba(79, 70, 229, 0); }
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-8px); }
  100% { transform: translateY(0px); }
}

/* Time warning animation */
@keyframes timeWarning {
  0% { color: #e11d48; }
  50% { color: #fef2f2; }
  100% { color: #e11d48; }
}

/* Word appear animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes staggerFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Utility classes for animations */
.correct-animation {
  animation: celebrate 0.5s ease, bounce 1s ease;
}

.incorrect-animation {
  animation: shake 0.5s ease;
}

.pulse-animation {
  animation: pulse 1.5s infinite;
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

.time-warning {
  animation: timeWarning 1s ease-in-out infinite;
}

.fade-in-up {
  animation: fadeInUp 0.3s ease-out forwards;
}

.stagger-fade-in > * {
  opacity: 0;
  animation: staggerFadeIn 0.3s ease-out forwards;
}

.stagger-fade-in > *:nth-child(1) { animation-delay: 0.05s; }
.stagger-fade-in > *:nth-child(2) { animation-delay: 0.1s; }
.stagger-fade-in > *:nth-child(3) { animation-delay: 0.15s; }
.stagger-fade-in > *:nth-child(4) { animation-delay: 0.2s; }
.stagger-fade-in > *:nth-child(5) { animation-delay: 0.25s; }
.stagger-fade-in > *:nth-child(6) { animation-delay: 0.3s; }
.stagger-fade-in > *:nth-child(7) { animation-delay: 0.35s; }
.stagger-fade-in > *:nth-child(8) { animation-delay: 0.4s; }
.stagger-fade-in > *:nth-child(9) { animation-delay: 0.45s; }
.stagger-fade-in > *:nth-child(10) { animation-delay: 0.5s; }

/* Drag & drop styles */
.draggable {
  cursor: grab;
  user-select: none;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.draggable:active {
  cursor: grabbing;
  transform: scale(1.05);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  z-index: 100;
}

.dragging {
  opacity: 0.7;
  transform: scale(1.05);
}

.drop-area {
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.drop-area.highlight {
  background-color: rgba(79, 70, 229, 0.1);
  border-color: #4f46e5;
} 
/* Word options animations */
@keyframes pulse-option {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.03);
  }
  100% {
    transform: scale(1);
  }
}

.pulse-option {
  animation: pulse-option 1.5s ease infinite;
}

/* Correct selection pulse */
@keyframes pulse-correct {
  0% {
    box-shadow: 0 0 0 0 rgba(74, 222, 128, 0.6);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(74, 222, 128, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(74, 222, 128, 0);
  }
}

.pulse-correct {
  animation: pulse-correct 1.5s ease infinite;
}

/* Incorrect selection shake */
@keyframes shake {
  0% { transform: translateX(0); }
  10% { transform: translateX(-5px); }
  20% { transform: translateX(5px); }
  30% { transform: translateX(-5px); }
  40% { transform: translateX(5px); }
  50% { transform: translateX(-5px); }
  60% { transform: translateX(5px); }
  70% { transform: translateX(-5px); }
  80% { transform: translateX(5px); }
  90% { transform: translateX(-5px); }
  100% { transform: translateX(0); }
}

.shake {
  animation: shake 0.8s ease-in-out;
}

/* Time running out pulse */
@keyframes pulse-warning {
  0% {
    box-shadow: 0 0 0 0 rgba(252, 165, 165, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(252, 165, 165, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(252, 165, 165, 0);
  }
}

.pulse-warning {
  animation: pulse-warning 1s ease infinite;
}

/* Prompt word emphasis */
@keyframes emphasis {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.emphasis {
  animation: emphasis 2s ease infinite;
}

/* Fade in for options */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease forwards;
}

/* Staggered fade in for options */
.stagger-fade-in {
  opacity: 0;
}

.stagger-fade-in:nth-child(1) { animation: fadeIn 0.5s ease forwards 0.1s; }
.stagger-fade-in:nth-child(2) { animation: fadeIn 0.5s ease forwards 0.2s; }
.stagger-fade-in:nth-child(3) { animation: fadeIn 0.5s ease forwards 0.3s; }
.stagger-fade-in:nth-child(4) { animation: fadeIn 0.5s ease forwards 0.4s; }
.stagger-fade-in:nth-child(5) { animation: fadeIn 0.5s ease forwards 0.5s; }
.stagger-fade-in:nth-child(6) { animation: fadeIn 0.5s ease forwards 0.6s; }
.stagger-fade-in:nth-child(7) { animation: fadeIn 0.5s ease forwards 0.7s; }
.stagger-fade-in:nth-child(8) { animation: fadeIn 0.5s ease forwards 0.8s; } 
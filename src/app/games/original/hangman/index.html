<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="styles.css">
    <title>Language Gems Hangman</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&family=Fredoka+One&display=swap" rel="stylesheet">
    <!-- Add modern fonts and animations libraries -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Righteous&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
    <script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>
    <script type="module">
        // Load environment variables from window.ENV (set by your server)
        const supabaseUrl = window.ENV?.SUPABASE_URL;
        const supabaseAnonKey = window.ENV?.SUPABASE_ANON_KEY;

        if (!supabaseUrl || !supabaseAnonKey) {
            console.warn('Missing Supabase credentials - some features may not work');
        }
    </script>
</head>
<body>
    <div class="game-container">
        <!-- Header Section -->
        <header class="game-header">
            <div class="nav-header">
                <h1 class="title animate__animated animate__fadeInDown">El ahorcado</h1>
            </div>
            <nav class="controls animate__animated animate__fadeInUp">
                <div class="control-item">
                    <a href="../index.html" class="btn-icon"><i class="fas fa-home"></i></a>
                    <div class="icon-label">home</div>
                </div>
                <div class="control-item">
                    <button id="soundToggleBtn" class="btn-icon"><i class="fas fa-volume-up"></i></button>
                    <div class="icon-label">sound</div>
                </div>
                <div class="control-item">
                    <button id="musicToggleBtn" class="btn-icon"><i class="fas fa-music"></i></button>
                    <div class="icon-label">music</div>
                </div>
                <div class="control-item">
                    <button id="fullscreenBtn" class="btn-icon"><i class="fas fa-expand"></i></button>
                    <div class="icon-label">fullscreen</div>
                </div>
                <div class="control-item">
                    <button id="highScoresBtn" class="btn-icon"><i class="fas fa-trophy"></i></button>
                    <div class="icon-label">scores</div>
                </div>
                <div class="control-item">
                    <button id="instructionsBtn" class="btn-icon"><i class="fas fa-book"></i></button>
                    <div class="icon-label">help</div>
                </div>
            </nav>
        </header>

        <!-- Game Stats Section -->
        <div class="game-stats">
            <div class="stat-item">
                <i class="fas fa-clock"></i>
                <div class="timer-container">
                    <div id="timer" class="stat-value" aria-label="Time remaining">00:15</div>
                    <div class="timer-progress-container">
                        <div class="timer-progress"></div>
                    </div>
                </div>
            </div>
            <div class="stat-item">
                <i class="fas fa-star"></i>
                <div id="currentScore" class="stat-value" aria-label="Current score">0</div>
            </div>
            <div class="stat-item">
                <i class="fas fa-bolt"></i>
                <div class="streak-container">
                    <div id="streak" class="stat-value" aria-label="Current streak">Streak: 0</div>
                    <div class="streak-progress"></div>
                </div>
            </div>
            <div class="stat-item">
                <div class="lives-container" aria-label="Remaining lives">
                    <i class="fas fa-heart heart-icon"></i>
                    <i class="fas fa-heart heart-icon"></i>
                    <i class="fas fa-heart heart-icon"></i>
                    <i class="fas fa-heart heart-icon"></i>
                    <i class="fas fa-heart heart-icon"></i>
                    <i class="fas fa-heart heart-icon"></i>
                </div>
            </div>
            <div class="stat-item">
                <i class="fas fa-trophy text-gold"></i>
                <div class="stat-value">Wins: <span id="wins" aria-label="Total wins">0</span></div>
            </div>
            <div class="stat-item">
                <i class="fas fa-times-circle text-red"></i>
                <div class="stat-value">Losses: <span id="losses" aria-label="Total losses">0</span></div>
            </div>
        </div>

        <!-- Game Settings Section -->
        <div class="game-settings">
            <div class="settings-group">
                <select id="categorySelect" class="select-style" aria-label="Select word category">
                    <option value="random">Random Category</option>
                    <option value="animals">Animals</option>
                    <option value="countries">Countries</option>
                    <option value="food">Food</option>
                    <option value="french">French</option>
                    <option value="spanish">Spanish</option>
                    <option value="german">German</option>
                </select>
                <input type="text" id="wordInput" placeholder="Enter Word or Phrase" class="input-style" aria-label="Enter custom word or phrase" />
            </div>
            <div class="button-group">
                <button id="startGameBtn" class="btn primary-btn pulse-animation" aria-label="Start new game">
                    <i class="fas fa-play"></i> Start Game
                </button>
                <button id="customListBtn" class="btn secondary-btn" aria-label="Open custom word list">
                    <i class="fas fa-list"></i> Custom Word List
                </button>
                <button id="hintBtn" class="btn hint-btn" disabled aria-label="Get a hint">
                    <i class="fas fa-lightbulb"></i> Get a Hint (<span id="hintsLeft">3</span>)
                </button>
            </div>
            <div class="difficulty-indicator" aria-label="Current difficulty level">
                <span class="difficulty-label">Difficulty:</span>
                <span id="currentDifficulty" class="difficulty-value">Beginner</span>
            </div>
        </div>

        <!-- Word Definition Section (Initially Hidden) -->
        <div id="wordDefinition" class="word-definition hidden" aria-live="polite">
            <h3>Word Definition</h3>
            <p id="definitionText"></p>
        </div>

        <!-- Accessibility Controls -->
        <div class="accessibility-controls">
            <button id="highContrastBtn" class="btn" aria-label="Toggle high contrast mode">
                <i class="fas fa-adjust"></i>
            </button>
            <button id="fontSizeBtn" class="btn" aria-label="Toggle font size">
                <i class="fas fa-text-height"></i>
            </button>
        </div>

        <!-- Main Game Area -->
        <div class="game-play-area">
            <div class="game-left">
                <div class="hangman-container">
                    <div class="hangman-scene">
                        <canvas id="hangmanCanvas" width="400" height="400"></canvas>
                        <!-- Add decorative elements -->
                        <div class="scene-decorations">
                            <div class="decoration cloud cloud-1"></div>
                            <div class="decoration cloud cloud-2"></div>
                            <div class="decoration bird bird-1"></div>
                            <div class="decoration bird bird-2"></div>
                        </div>
                    </div>
                    <div id="message" class="message-display"></div>
                </div>
            </div>
            <div class="game-right">
                <div class="word-display">
                    <h2 id="displayWord" class="glow-text">_ _ _ _ _</h2>
                    <p id="categoryDisplay" class="category-display"></p>
                </div>
                <div id="lettersContainer" class="letters-container">
                    <!-- Letters will be added here dynamically -->
                </div>
            </div>
        </div>

        <!-- Language selector -->
        <div class="language-selector">
            <button class="lang-btn active" data-lang="en">
                <img src="../assets/flags/en.svg" alt="English" />
            </button>
            <button class="lang-btn" data-lang="es">
                <img src="../assets/flags/es.svg" alt="Spanish" />
            </button>
            <button class="lang-btn" data-lang="fr">
                <img src="../assets/flags/fr.svg" alt="French" />
            </button>
            <button class="lang-btn" data-lang="de">
                <img src="../assets/flags/de.svg" alt="German" />
            </button>
        </div>

        <!-- Instructions Modal -->
        <div id="instructionsModal" class="modal">
            <div class="modal-content">
                <span class="close-modal">&times;</span>
                <h2><i class="fas fa-book"></i> How to Play</h2>
                <div class="instructions-grid">
                    <div class="instruction-item">
                        <i class="fas fa-play"></i>
                        <p>1. Choose a category or enter your own word/phrase, then click Start Game</p>
                    </div>
                    <div class="instruction-item">
                        <i class="fas fa-keyboard"></i>
                        <p>2. Guess letters by clicking them or using your keyboard</p>
                    </div>
                    <div class="instruction-item">
                        <i class="fas fa-lightbulb"></i>
                        <p>3. Use the hint button to reveal one letter (3 hints per game)</p>
                    </div>
                    <div class="instruction-item">
                        <i class="fas fa-trophy"></i>
                        <p>4. Build your streak to earn more points!</p>
                    </div>
                </div>
                <button class="btn close-btn">Got it!</button>
            </div>
        </div>

        <!-- Victory Modal -->
        <div id="victoryModal" class="modal">
            <div class="modal-content victory">
                <span class="close-modal">&times;</span>
                <div class="victory-header">
                    <h2><i class="fas fa-crown"></i> Victory!</h2>
                    <!-- Add celebratory animation -->
                    <lottie-player src="https://assets5.lottiefiles.com/packages/lf20_jnlvhkgm.json" 
                        background="transparent" speed="1" style="width: 200px; height: 200px; margin: 0 auto;" autoplay>
                    </lottie-player>
                </div>
                <div class="victory-stats">
                    <div id="victoryStats"></div>
                    <div class="victory-buttons">
                        <button id="nextWordBtn" class="btn next-word-btn">
                            <i class="fas fa-forward"></i> Next Word
                        </button>
                        <button class="btn close-btn">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Game Over Modal -->
        <div id="gameOverModal" class="modal">
            <div class="modal-content game-over">
                <span class="close-modal">&times;</span>
                <h2><i class="fas fa-skull-crossbones"></i> Game Over</h2>
                <!-- Add sad animation -->
                <lottie-player src="https://assets9.lottiefiles.com/packages/lf20_buhby1dq.json" 
                    background="transparent" speed="1" style="width: 150px; height: 150px; margin: 0 auto;" autoplay>
                </lottie-player>
                <p id="gameOverText"></p>
                <div class="game-over-buttons">
                    <button id="newGameBtn" class="btn try-again-btn">
                        <i class="fas fa-redo"></i> Try Again
                    </button>
                </div>
            </div>
        </div>

        <!-- Custom Word List Modal -->
        <div id="customListModal" class="modal">
            <div class="modal-content">
                <span class="close-modal">&times;</span>
                <h2><i class="fas fa-list"></i> Custom Word List</h2>
                <div class="custom-list-instructions">
                    <p>Add words to create your own custom word list</p>
                </div>
                <div class="custom-list-controls">
                    <div class="word-input-group">
                        <input type="text" id="customWordInput" placeholder="Enter a word" class="input-style">
                        <button id="addToListBtn" class="btn">
                            <i class="fas fa-plus"></i> Add Word
                        </button>
                    </div>
                    <div id="customWordDisplay" class="word-display">
                        <!-- Words will be added here dynamically -->
                    </div>
                    <div class="button-group">
                        <button id="startCustomListBtn" class="btn primary-btn">
                            <i class="fas fa-play"></i> Start Game with Custom Words
                        </button>
                    </div>
                </div>
                <button class="btn close-btn">Close</button>
            </div>
        </div>

        <!-- High Scores Modal -->
        <div id="highScoresModal" class="modal">
            <div class="modal-content">
                <span class="close-modal">&times;</span>
                <h2><i class="fas fa-trophy"></i> High Scores</h2>
                <!-- Add trophy animation -->
                <lottie-player src="https://assets7.lottiefiles.com/packages/lf20_6e0qqtpa.json" 
                    background="transparent" speed="1" style="width: 120px; height: 120px; margin: 0 auto;" autoplay>
                </lottie-player>
                <div id="highScoresList">
                    <p>Current Streak: <span id="currentStreakDisplay">0</span></p>
                    <p>Total Wins: <span id="totalWinsDisplay">0</span></p>
                    <p>Total Losses: <span id="totalLossesDisplay">0</span></p>
                </div>
                <button class="btn close-btn">Close</button>
            </div>
        </div>

        <!-- Sound Effects -->
        <audio id="correctSound" src="assets/sounds/correct.mp3" preload="auto"></audio>
        <audio id="wrongSound" src="assets/sounds/wrong.mp3" preload="auto"></audio>
        <audio id="winSound" src="assets/sounds/win.mp3" preload="auto"></audio>
        <audio id="loseSound" src="assets/sounds/lose.mp3" preload="auto"></audio>
        <audio id="backgroundMusic" src="assets/music/background.mp3" preload="auto" loop></audio>

        <!-- Scripts -->
        <script type="module" src="script.js"></script>
    </div>
</body>
</html>
:root {
    --primary-color: #6a5acd;
    --primary-dark: #483d8b;
    --primary-light: #8a7dde;
    --secondary-color: #ff6b6b;
    --accent-color: #ffd166;
    --text-color: #333;
    --text-light: #f8f9fa;
    --background-color: #f0f2f5;
    --card-bg: #ffffff;
    --success-color: #06d6a0;
    --error-color: #ef476f;
    --warning-color: #ffd166;
    --border-radius: 8px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    
    /* New modern color palette */
    --modern-primary: #764abc;
    --modern-primary-dark: #5c3997;
    --modern-primary-light: #9071d8;
    --modern-secondary: #ff6b6b;
    --modern-accent: #ffc857;
    --modern-success: #37b37e;
    --modern-info: #4c9aff;
    --modern-warning: #ffab00;
    --modern-error: #ff5630;
    --modern-gray-100: #f5f6f7;
    --modern-gray-200: #eaecef;
    --modern-gray-300: #dfe1e6;
    --modern-gray-400: #c1c7d0;
    --modern-gray-500: #a5adba;
    --modern-gray-700: #505f79;
    --modern-gray-900: #172b4d;
    
    /* Enhanced shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--modern-primary), var(--modern-primary-dark));
    --gradient-secondary: linear-gradient(135deg, var(--modern-secondary), #e05d5d);
    --gradient-accent: linear-gradient(135deg, var(--modern-accent), #e5b64e);
    
    /* Border radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-2xl: 24px;
    --radius-circle: 50%;
}

[data-theme="dark"] {
    --primary-color: #34495e;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --text-color: #ecf0f1;
    --background-color: #2c3e50;
    --shadow-color: rgba(0, 0, 0, 0.3);
}

/* Base styles */
body {
    font-family: 'Montserrat', 'Poppins', sans-serif;
    background: var(--gradient-primary);
    background-attachment: fixed;
    color: var(--text-color);
    line-height: 1.5;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0.5rem;
    margin: 0;
    overflow: hidden;
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%232f80ed' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
}

.game-container {
    background-color: rgba(255, 255, 255, 0.03);
    border-radius: var(--radius-xl);
    backdrop-filter: blur(10px);
    box-shadow: 
        0 15px 25px rgba(0, 0, 0, 0.1),
        0 5px 10px rgba(0, 0, 0, 0.05),
        inset 0 0 0 1px rgba(255, 255, 255, 0.1);
    max-width: 1200px;
    width: 98%;
    margin: 0 auto;
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    max-height: 98vh;
    overflow: hidden;
}

.game-container:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-2xl);
}

/* Header styles */
.game-header {
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    padding: 0.5rem 1.5rem;
    margin-bottom: 0.75rem;
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    box-shadow: var(--shadow-md);
    z-index: 1;
}

.game-header::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, 
        var(--modern-secondary) 0%, 
        var(--modern-primary-light) 25%, 
        var(--modern-accent) 50%, 
        var(--modern-success) 75%, 
        var(--modern-info) 100%);
    z-index: 1;
}

.nav-header {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.title {
    font-family: 'Righteous', 'Fredoka One', cursive;
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(135deg, #fff, #ccc);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    position: relative;
    display: inline-block;
    animation: shimmer 2s infinite;
    letter-spacing: 0.5px;
}

@keyframes shimmer {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

.controls {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.control-item {
    position: relative;
    margin: 0 0.25rem;
}

.control-item:hover {
    transform: translateY(-3px);
}

.btn-icon {
    background-color: rgba(255, 255, 255, 0.15);
    color: var(--text-light);
    border: none;
    border-radius: var(--radius-circle);
    width: 45px;
    height: 45px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    font-size: 1.1rem;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-icon::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease-out, height 0.3s ease-out;
}

.btn-icon:hover {
    background-color: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.btn-icon:hover::after {
    width: 150%;
    height: 150%;
}

.btn-icon:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.icon-label {
    font-size: 0.75rem;
    color: var(--text-light);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.8;
}

/* Game Stats Section */
.game-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.1));
    border-radius: var(--radius-lg);
    padding: 0.5rem;
    margin-bottom: 0.75rem;
    position: relative;
    box-shadow: var(--shadow-sm);
    justify-content: space-around;
}

.game-stats::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M0 0h20v20H0V0zm10 17.5c4.142 0 7.5-3.358 7.5-7.5S14.142 2.5 10 2.5 2.5 5.858 2.5 10s3.358 7.5 7.5 7.5z'/%3E%3C/g%3E%3C/svg%3E");
    z-index: 0;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 10px;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 8px 15px;
    border-radius: var(--radius-md);
    position: relative;
    z-index: 1;
    backdrop-filter: blur(8px);
    transition: transform 0.3s ease, background-color 0.3s ease;
}

.stat-item:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.stat-value {
    font-weight: 600;
    font-size: 1.1rem;
}

.text-gold {
    color: var(--modern-accent);
    text-shadow: 0 0 5px rgba(255, 200, 87, 0.3);
}

.text-red {
    color: var(--modern-error);
    text-shadow: 0 0 5px rgba(255, 86, 48, 0.3);
}

/* Game Settings Section */
.game-settings {
    margin-bottom: 0.75rem;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 0.5rem;
    justify-content: space-between;
    padding: 0.5rem;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-lg);
}

.game-settings::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 10%;
    width: 80%;
    height: 5px;
    background: linear-gradient(to right, var(--modern-gray-200), var(--modern-gray-300), var(--modern-gray-200));
    border-radius: var(--radius-md);
}

.settings-group {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.select-style, .input-style {
    padding: 12px 18px;
    border: 2px solid var(--modern-gray-300);
    border-radius: var(--radius-md);
    font-family: inherit;
    font-size: 1rem;
    flex: 1;
    min-width: 150px;
    transition: all 0.3s ease;
    background-color: var(--modern-gray-100);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
}

.select-style:focus, .input-style:focus {
    border-color: var(--modern-primary);
    outline: none;
    box-shadow: 0 0 0 3px rgba(118, 74, 188, 0.2);
}

.button-group {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: var(--radius-md);
    font-family: inherit;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    display: flex;
    align-items: center;
    gap: 10px;
    position: relative;
    overflow: hidden;
}

.btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease-out, height 0.6s ease-out;
    z-index: 1;
}

.btn:hover::after {
    width: 300%;
    height: 300%;
}

.btn * {
    position: relative;
    z-index: 2;
}

.primary-btn {
    background: var(--gradient-primary);
    color: var(--text-light);
    box-shadow: 0 4px 10px rgba(118, 74, 188, 0.3);
}

.primary-btn:hover {
    background: linear-gradient(135deg, var(--modern-primary-dark), var(--modern-primary));
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(118, 74, 188, 0.4);
}

.primary-btn:active {
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(118, 74, 188, 0.3);
}

.secondary-btn {
    background: var(--gradient-secondary);
    color: var(--text-light);
    box-shadow: 0 4px 10px rgba(255, 107, 107, 0.3);
}

.secondary-btn:hover {
    background: linear-gradient(135deg, #e05d5d, var(--modern-secondary));
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(255, 107, 107, 0.4);
}

.hint-btn {
    background: var(--gradient-accent);
    color: var(--modern-gray-900);
    box-shadow: 0 4px 10px rgba(255, 200, 87, 0.3);
}

.hint-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #e5b64e, var(--modern-accent));
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(255, 200, 87, 0.4);
}

.hint-btn:disabled {
    background: var(--modern-gray-300);
    color: var(--modern-gray-500);
    box-shadow: none;
    cursor: not-allowed;
}

/* Main Game Area */
.game-play-area {
    display: flex;
    gap: 1rem;
    flex: 1;
    min-height: 0;
    overflow: hidden;
}

.game-left, .game-right {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
}

.hangman-container {
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.hangman-scene {
    flex: 1;
    position: relative;
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: linear-gradient(135deg, #88d0ff, #5f89d6);
    box-shadow: var(--shadow-lg);
    margin-bottom: 0.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 0;
}

#hangmanCanvas {
    width: 400px;
    height: 400px;
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.message-display {
    font-size: 1.2rem;
    font-weight: 600;
    text-align: center;
    min-height: 30px;
    padding: 10px 15px;
    border-radius: var(--radius-md);
    background-color: var(--modern-gray-200);
    transition: all 0.3s ease;
}

.message-display.success {
    color: var(--modern-success);
    background-color: rgba(55, 179, 126, 0.1);
    border-left: 4px solid var(--modern-success);
}

.message-display.error {
    color: var(--modern-error);
    background-color: rgba(255, 86, 48, 0.1);
    border-left: 4px solid var(--modern-error);
}

.word-display {
    margin-bottom: 1rem;
    text-align: center;
    position: relative;
    padding: 1rem;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

.word-display::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, 
        var(--modern-secondary) 0%, 
        var(--modern-primary-light) 25%, 
        var(--modern-accent) 50%, 
        var(--modern-success) 75%, 
        var(--modern-info) 100%);
}

.glow-text {
    font-size: 2.8rem;
    font-weight: 700;
    letter-spacing: 8px;
    color: var(--modern-primary);
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
}

.glow-text::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(to right, transparent, var(--modern-primary-light), transparent);
    border-radius: 3px;
}

@keyframes textShadowPulse {
    0% {
        text-shadow: 0 0 5px rgba(118, 74, 188, 0.2);
    }
    50% {
        text-shadow: 0 0 20px rgba(118, 74, 188, 0.6);
    }
    100% {
        text-shadow: 0 0 5px rgba(118, 74, 188, 0.2);
    }
}

.glow-text {
    animation: textShadowPulse 3s infinite;
}

.category-display {
    font-size: 1.1rem;
    color: var(--modern-gray-700);
    font-style: italic;
    position: relative;
    display: inline-block;
    padding: 5px 15px;
    border-radius: var(--radius-md);
    background-color: var(--modern-gray-200);
}

.letters-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(2.5rem, 1fr));
    gap: 0.4rem;
    padding: 0.5rem;
    overflow-y: auto;
    max-height: 220px;
    scrollbar-width: thin;
    scrollbar-color: var(--modern-primary-light) transparent;
}

.letters-container::-webkit-scrollbar {
    width: 6px;
}

.letters-container::-webkit-scrollbar-track {
    background: transparent;
}

.letters-container::-webkit-scrollbar-thumb {
    background-color: var(--modern-primary-light);
    border-radius: 20px;
}

.letter-button {
    font-size: 1.2rem;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: var(--radius-circle);
    background: var(--gradient-primary);
    color: white;
    border: none;
    cursor: pointer;
    font-weight: 600;
    position: relative;
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: var(--shadow-sm);
    text-transform: uppercase;
}

.letter-button::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 60%);
    z-index: 1;
}

.letter-button:hover:not(:disabled) {
    transform: translateY(-5px) rotateY(10deg);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.letter-button:active:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.letter-button:disabled {
    cursor: default;
    opacity: 0.6;
    transform: scale(0.95);
}

.letter-button.correct {
    background: linear-gradient(135deg, var(--modern-success), #2ea26b);
    box-shadow: 0 4px 10px rgba(55, 179, 126, 0.3);
    animation: correct-letter 0.5s ease-out;
}

@keyframes correct-letter {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.15);
        box-shadow: 0 0 20px rgba(55, 179, 126, 0.7);
    }
    100% {
        transform: scale(1);
    }
}

.letter-button.incorrect {
    background: linear-gradient(135deg, var(--modern-error), #e64b24);
    box-shadow: 0 4px 10px rgba(255, 86, 48, 0.3);
    animation: incorrect-letter 0.5s ease-out;
}

@keyframes incorrect-letter {
    0%, 20%, 40%, 60%, 80%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-3px);
    }
    15%, 35%, 55%, 75%, 95% {
        transform: translateX(3px);
    }
}

/* Special character letter buttons */
.letter-button[data-letter="Á"],
.letter-button[data-letter="É"],
.letter-button[data-letter="Í"],
.letter-button[data-letter="Ó"],
.letter-button[data-letter="Ú"],
.letter-button[data-letter="Ü"],
.letter-button[data-letter="Ñ"],
.letter-button[data-letter="Ä"],
.letter-button[data-letter="Ö"],
.letter-button[data-letter="Ü"],
.letter-button[data-letter="ß"],
.letter-button[data-letter="É"],
.letter-button[data-letter="È"],
.letter-button[data-letter="Ê"],
.letter-button[data-letter="Ë"],
.letter-button[data-letter="À"],
.letter-button[data-letter="Â"],
.letter-button[data-letter="Î"],
.letter-button[data-letter="Ï"],
.letter-button[data-letter="Ô"],
.letter-button[data-letter="Ù"],
.letter-button[data-letter="Û"],
.letter-button[data-letter="Ç"],
.letter-button[data-letter="Œ"],
.letter-button[data-letter="¿"],
.letter-button[data-letter="¡"] {
    background: var(--gradient-accent);
    font-weight: 700;
    animation: special-letter-pop 0.5s ease-out;
}

@keyframes special-letter-pop {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(23, 43, 77, 0.7);
    z-index: 1000;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(5px);
    transition: opacity 0.3s ease;
    opacity: 0;
}

.modal.show {
    display: flex;
    animation: modalFadeIn 0.3s forwards;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-content {
    background-color: var(--card-bg);
    border-radius: var(--radius-lg);
    padding: 35px;
    max-width: 550px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    box-shadow: var(--shadow-xl);
    transform: translateY(20px);
    transition: transform 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.modal.show .modal-content {
    transform: translateY(0);
}

.close-modal {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 1.8rem;
    cursor: pointer;
    color: var(--modern-gray-500);
    transition: all 0.2s ease;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.close-modal:hover {
    color: var(--modern-error);
    background-color: rgba(255, 86, 48, 0.1);
    transform: rotate(90deg);
}

.modal-content h2 {
    margin-bottom: 25px;
    color: var(--modern-primary);
    text-align: center;
    font-size: 2rem;
    position: relative;
    padding-bottom: 15px;
}

.modal-content h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(to right, var(--modern-primary-light), var(--modern-primary-dark));
    border-radius: 3px;
}

.modal-content h2 i {
    margin-right: 10px;
    background: linear-gradient(135deg, var(--modern-primary-light), var(--modern-primary-dark));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.instructions-grid {
    display: grid;
    gap: 20px;
    margin-bottom: 30px;
}

.instruction-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 15px;
    border-radius: var(--radius-md);
    background-color: var(--modern-gray-100);
    border-left: 4px solid var(--modern-primary);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.instruction-item:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.instruction-item i {
    color: white;
    font-size: 1.2rem;
    margin-top: 3px;
    background: var(--gradient-primary);
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.instruction-item p {
    margin: 0;
    font-size: 1.05rem;
}

.close-btn {
    background-color: var(--modern-gray-500);
    color: var(--text-light);
    margin-top: 25px;
    width: 100%;
    justify-content: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.close-btn:hover {
    background-color: var(--modern-gray-700);
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.close-btn:active {
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

/* Victory Modal */
.victory-header {
    text-align: center;
    margin-bottom: 20px;
}

.victory-header h2 {
    color: var(--success-color);
    margin-bottom: 10px;
}

.victory-stats {
    margin-bottom: 20px;
}

.victory-buttons {
    display: flex;
    gap: 10px;
}

.next-word-btn {
    background-color: var(--primary-color);
    color: var(--text-light);
    flex: 1;
    justify-content: center;
}

.next-word-btn:hover {
    background-color: var(--primary-dark);
}

/* Game Over Modal */
.game-over h2 {
    color: var(--error-color);
}

.game-over p {
    text-align: center;
    font-size: 1.1rem;
    margin: 20px 0;
    padding: 15px;
    background-color: rgba(255, 86, 48, 0.1);
    border-radius: var(--radius-md);
    border-left: 4px solid var(--modern-error);
}

.game-over-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.try-again-btn {
    background: var(--gradient-secondary);
    color: var(--text-light);
    padding: 15px 30px;
    font-size: 1.1rem;
    box-shadow: 0 4px 10px rgba(255, 107, 107, 0.3);
    transform: translateY(0);
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
}

.try-again-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(255, 107, 107, 0.4);
}

.try-again-btn:active {
    transform: translateY(-2px);
}

.try-again-btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 60%);
    z-index: 1;
}

.try-again-btn i {
    margin-right: 10px;
    position: relative;
    z-index: 2;
}

/* Custom Word List Modal */
.custom-list-instructions {
    margin-bottom: 15px;
}

.word-input-group {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.word-display {
    background-color: #f8f9fa;
    border-radius: var(--border-radius);
    padding: 15px;
    margin-bottom: 15px;
    min-height: 100px;
    max-height: 200px;
    overflow-y: auto;
}

.saved-lists-section {
    margin-top: 20px;
    border-top: 1px solid #eee;
    padding-top: 20px;
}

.saved-lists-section h3 {
    margin-bottom: 15px;
    color: var(--primary-color);
}

.saved-lists-container {
    max-height: 200px;
    overflow-y: auto;
}

.word-list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.word-info {
    flex: 1;
}

.word-text {
    font-weight: 600;
    display: block;
}

.word-meta {
    font-size: 0.8rem;
    color: #666;
}

.actions {
    display: flex;
    gap: 5px;
}

.use-list-btn, .delete-btn {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    padding: 5px;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.use-list-btn {
    color: var(--primary-color);
}

.delete-btn {
    color: var(--error-color);
}

/* Animations */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .game-play-area {
        flex-direction: column;
    }
    
    .title {
        font-size: 1.4rem;
    }
    
    .glow-text {
        font-size: 1.5rem;
    }
    
    .letters-container {
        grid-template-columns: repeat(auto-fit, minmax(2.2rem, 1fr));
        gap: 0.3rem;
        max-height: 180px;
    }
    
    .letter-button {
        font-size: 1rem;
        width: 2.2rem;
        height: 2.2rem;
    }
    
    #hangmanCanvas {
        width: 300px;
        height: 300px;
    }
    
    .game-container {
        padding: 0.5rem;
        max-height: 100vh;
    }
    
    .game-header, .game-stats, .game-settings {
        padding: 0.4rem;
        margin-bottom: 0.5rem;
    }
    
    .language-selector {
        margin-top: 0.3rem;
    }
    
    .lang-btn {
        width: 2rem;
        height: 2rem;
    }
}

/* For very small screens */
@media (max-width: 480px) {
    .title {
        font-size: 1.2rem;
    }
    
    #hangmanCanvas {
        width: 250px;
        height: 250px;
    }
    
    .button-group {
        gap: 0.25rem;
    }
    
    .btn {
        padding: 0.4rem 0.6rem;
        font-size: 0.8rem;
    }
    
    .letters-container {
        grid-template-columns: repeat(auto-fit, minmax(1.8rem, 1fr));
        gap: 0.2rem;
    }
    
    .letter-button {
        font-size: 0.8rem;
        width: 1.8rem;
        height: 1.8rem;
    }
}

/* Custom Word Chip Styles */
.word-chip {
    display: inline-flex;
    align-items: center;
    background: var(--gradient-primary);
    color: var(--text-light);
    padding: 8px 15px;
    border-radius: 50px;
    margin: 6px;
    font-size: 0.95rem;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.word-chip::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 60%);
}

.word-chip:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.word-chip .remove-word {
    margin-left: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    transition: all 0.2s ease;
}

.word-chip .remove-word:hover {
    background-color: rgba(255, 255, 255, 0.4);
    transform: rotate(90deg);
}

/* High Scores List Styles */
#highScoresList {
    background-color: var(--modern-gray-100);
    border-radius: var(--radius-md);
    padding: 20px;
    margin-top: 20px;
    box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.05);
}

#highScoresList p {
    margin-bottom: 15px;
    font-size: 1.1rem;
    display: flex;
    justify-content: space-between;
    padding: 10px 15px;
    border-radius: var(--radius-md);
    background-color: white;
    box-shadow: var(--shadow-sm);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

#highScoresList p:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

#highScoresList p:last-child {
    margin-bottom: 0;
}

#highScoresList span {
    font-weight: 700;
    color: var(--modern-primary);
}

/* Hidden class */
.hidden {
    display: none !important;
}

/* Confetti styles */
.confetti-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
    z-index: 10;
}

.confetti {
    position: absolute;
    width: 10px;
    height: 20px;
    background-color: var(--modern-accent);
    opacity: 0.8;
    animation: confetti-fall 4s linear forwards;
}

@keyframes confetti-fall {
    0% {
        transform: translateY(0) rotate(0deg) scale(1);
        opacity: 1;
    }
    25% {
        transform: translateY(25vh) rotate(180deg) scale(1.1);
        opacity: 0.8;
    }
    50% {
        transform: translateY(50vh) rotate(360deg) scale(1);
        opacity: 0.6;
    }
    75% {
        transform: translateY(75vh) rotate(540deg) scale(0.9);
        opacity: 0.4;
    }
    100% {
        transform: translateY(100vh) rotate(720deg) scale(0.8);
        opacity: 0;
    }
}

/* Notification styles */
.notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 15px 20px;
    background-color: #333;
    color: white;
    border-radius: 5px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.3s, transform 0.3s;
    z-index: 1000;
}

.notification.show {
    opacity: 1;
    transform: translateY(0);
}

.notification.info {
    background-color: #3498db;
}

.notification.success {
    background-color: #2ecc71;
}

.notification.error {
    background-color: #e74c3c;
}

/* Scene Decorations for Hangman Canvas */
.hangman-scene {
    position: relative;
}

.scene-decorations {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
    overflow: hidden;
}

.decoration {
    position: absolute;
    opacity: 0.6;
}

/* Cloud Animations */
.cloud {
    background-color: white;
    border-radius: 50%;
    position: absolute;
}

.cloud::before,
.cloud::after {
    content: '';
    position: absolute;
    background-color: white;
    border-radius: 50%;
}

.cloud-1 {
    width: 40px;
    height: 20px;
    top: 30px;
    left: 20px;
    animation: float-cloud 20s linear infinite;
}

.cloud-1::before {
    width: 25px;
    height: 25px;
    top: -15px;
    left: 5px;
}

.cloud-1::after {
    width: 20px;
    height: 20px;
    top: -10px;
    left: 20px;
}

.cloud-2 {
    width: 30px;
    height: 15px;
    top: 60px;
    right: 30px;
    animation: float-cloud 15s linear infinite;
    animation-delay: -5s;
}

.cloud-2::before {
    width: 20px;
    height: 20px;
    top: -12px;
    left: 3px;
}

.cloud-2::after {
    width: 15px;
    height: 15px;
    top: -8px;
    left: 15px;
}

@keyframes float-cloud {
    0% {
        transform: translateX(-50px);
    }
    100% {
        transform: translateX(350px);
    }
}

/* Bird Animations */
.bird {
    position: absolute;
    width: 15px;
    height: 10px;
    transform: rotate(-5deg);
}

.bird::before,
.bird::after {
    content: '';
    position: absolute;
    background-color: #333;
    width: 15px;
    height: 3px;
    top: 0;
    left: 0;
    transform-origin: right center;
    animation: flap-wings 0.5s infinite alternate;
}

.bird::after {
    transform-origin: right center;
    transform: rotate(20deg);
    animation-delay: -0.25s;
}

.bird::before {
    transform-origin: right center;
    transform: rotate(-20deg);
}

.bird-1 {
    top: 50px;
    left: 100px;
    animation: fly-bird 15s linear infinite;
}

.bird-2 {
    top: 70px;
    left: 150px;
    transform: scale(0.8) rotate(-5deg);
    animation: fly-bird 20s linear infinite;
    animation-delay: -5s;
}

@keyframes flap-wings {
    0% {
        transform: rotate(10deg);
    }
    100% {
        transform: rotate(-30deg);
    }
}

@keyframes fly-bird {
    0% {
        transform: translate(-100px, 10px) rotate(-5deg);
    }
    50% {
        transform: translate(200px, -30px) rotate(-5deg);
    }
    100% {
        transform: translate(400px, 10px) rotate(-5deg);
    }
}

/* Victory Animation Enhancement */
@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

lottie-player {
    animation: float 3s ease-in-out infinite;
}

/* Improved Confetti Animation */
.confetti {
    position: absolute;
    width: 10px;
    height: 20px;
    background-color: var(--modern-accent);
    opacity: 0.8;
    animation: confetti-fall 4s linear forwards;
}

@keyframes confetti-fall {
    0% {
        transform: translateY(0) rotate(0deg) scale(1);
        opacity: 1;
    }
    25% {
        transform: translateY(25vh) rotate(180deg) scale(1.1);
        opacity: 0.8;
    }
    50% {
        transform: translateY(50vh) rotate(360deg) scale(1);
        opacity: 0.6;
    }
    75% {
        transform: translateY(75vh) rotate(540deg) scale(0.9);
        opacity: 0.4;
    }
    100% {
        transform: translateY(100vh) rotate(720deg) scale(0.8);
        opacity: 0;
    }
}

/* Custom Word Chip Enhanced Styles */
.word-chip {
    display: inline-flex;
    align-items: center;
    background: var(--gradient-primary);
    color: var(--text-light);
    padding: 8px 15px;
    border-radius: 50px;
    margin: 6px;
    font-size: 0.95rem;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.word-chip::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 60%);
}

.word-chip:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.word-chip .remove-word {
    margin-left: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    transition: all 0.2s ease;
}

.word-chip .remove-word:hover {
    background-color: rgba(255, 255, 255, 0.4);
    transform: rotate(90deg);
}

/* Button pop animation */
@keyframes button-pop {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Modern shadow utility for our special elements */
.shadow-2xl {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Add styling for language selector */
.language-selector {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
    padding: 0.25rem;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-lg);
}

.lang-btn {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: var(--radius-circle);
    border: 2px solid transparent;
    padding: 2px;
    background-color: rgba(255, 255, 255, 0.2);
    cursor: pointer;
    transition: all 0.2s ease;
    overflow: hidden;
}

.lang-btn img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.lang-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.lang-btn.active {
    border-color: var(--modern-accent);
    box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.3);
    transform: scale(1.1);
}

/* Timer Progress Bar */
.timer-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.timer-progress-container {
    width: 100%;
    height: 4px;
    background-color: #ddd;
    border-radius: 2px;
    overflow: hidden;
}

.timer-progress {
    height: 100%;
    width: 100%;
    background-color: #4CAF50;
    transition: width 1s linear, background-color 0.3s ease;
}

/* Lives Display */
.lives-container {
    display: flex;
    gap: 5px;
}

.heart-icon {
    color: #ff4444;
    font-size: 1.2em;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.heart-icon.lost {
    transform: scale(0.8);
    opacity: 0.3;
}

/* Streak Progress */
.streak-container {
    position: relative;
}

.streak-progress {
    position: absolute;
    bottom: -2px;
    left: 0;
    height: 2px;
    background: linear-gradient(to right, #4CAF50, #FFA500);
    transition: width 0.3s ease;
}

/* Points Animation */
.points-animation {
    position: absolute;
    color: #4CAF50;
    font-weight: bold;
    animation: pointsFadeUp 1s ease-out forwards;
    pointer-events: none;
}

@keyframes pointsFadeUp {
    0% {
        opacity: 1;
        transform: translateY(0);
    }
    100% {
        opacity: 0;
        transform: translateY(-20px);
    }
}

/* Difficulty Indicator */
.difficulty-indicator {
    margin-top: 10px;
    text-align: center;
    font-size: 0.9em;
}

.difficulty-label {
    color: #666;
    margin-right: 5px;
}

.difficulty-value {
    font-weight: bold;
    color: #4CAF50;
}

/* Word Definition */
.word-definition {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: opacity 0.3s ease;
}

.word-definition.hidden {
    display: none;
}

.word-definition h3 {
    color: #333;
    margin: 0 0 10px 0;
}

.word-definition p {
    color: #666;
    line-height: 1.4;
    margin: 0;
}

/* Accessibility Controls */
.accessibility-controls {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    gap: 10px;
    z-index: 1000;
}

.accessibility-controls .btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.accessibility-controls .btn:hover {
    background: #f0f0f0;
    transform: scale(1.1);
}

/* High Contrast Mode */
body.high-contrast {
    background: #000;
    color: #fff;
}

body.high-contrast .btn {
    background: #fff;
    color: #000;
    border: 2px solid #fff;
}

body.high-contrast .letter-button {
    background: #fff;
    color: #000;
    border: 2px solid #fff;
}

body.high-contrast .letter-button.guessed {
    background: #444;
    color: #fff;
}

/* Large Font Mode */
body.large-font {
    font-size: 120%;
}

body.large-font .letter-button {
    font-size: 1.2em;
}

/* Responsive Design Improvements */
@media (max-width: 768px) {
    .game-stats {
        flex-wrap: wrap;
        justify-content: center;
        gap: 15px;
    }

    .stat-item {
        flex: 0 0 calc(33.33% - 20px);
    }

    .lives-container {
        justify-content: center;
    }

    .accessibility-controls {
        bottom: 10px;
        right: 10px;
    }
}

@media (max-width: 480px) {
    .stat-item {
        flex: 0 0 calc(50% - 20px);
    }

    .heart-icon {
        font-size: 1em;
    }

    .accessibility-controls .btn {
        width: 35px;
        height: 35px;
    }
}


* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #1a2a6c, #b21f1f, #fdbb2d);
    color: #333;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    animation: gradientBG 15s ease infinite;
    background-size: 400% 400%;
}

@keyframes gradientBG {
    0% { background-position: 0% 50% }
    50% { background-position: 100% 50% }
    100% { background-position: 0% 50% }
}

.game-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    text-align: center;
    width: 95%;
    max-width: 700px;
    backdrop-filter: blur(10px);
    transform: translateY(0);
    transition: transform 0.3s ease;
}

.game-container:hover {
    transform: translateY(-5px);
}

.title {
    font-size: 2.8rem;
    margin-bottom: 30px;
    color: #1a2a6c;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.word-container {
    font-size: 2.5rem;
    margin: 30px 0;
    perspective: 1000px;
}

#scrambledWord {
    display: inline-block;
    animation: flipIn 0.6s ease-out, shuffle 1s ease-in-out infinite;
    color: #b21f1f;
    letter-spacing: 5px;
}

@keyframes flipIn {
    0% { transform: rotateX(90deg); opacity: 0; }
    100% { transform: rotateX(0deg); opacity: 1; }
}

/* Word shuffling animation */
@keyframes shuffle {
    0% { transform: translateY(0); }
    25% { transform: translateY(-10px); }
    50% { transform: translateY(0); }
    75% { transform: translateY(10px); }
    100% { transform: translateY(0); }
}

.input-container {
    margin: 30px 0;
}

#answerInput {
    padding: 15px 20px;
    border: 2px solid #ddd;
    border-radius: 10px;
    width: 60%;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    margin-bottom: 15px;
}

#answerInput:focus {
    outline: none;
    border-color: #1a2a6c;
    box-shadow: 0 0 10px rgba(26, 42, 108, 0.2);
}

.btn {
    padding: 12px 25px;
    border: none;
    border-radius: 10px;
    background: linear-gradient(135deg, #1a2a6c, #b21f1f);
    color: white;
    font-size: 1.1rem;
    cursor: pointer;
    margin: 8px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Button click animation */
.btn:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
}

.scoreboard {
    background: rgba(26, 42, 108, 0.1);
    padding: 20px;
    border-radius: 15px;
    margin: 25px 0;
    display: flex;
    justify-content: space-around;
    font-size: 1.2rem;
}

.score-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.score-label {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 5px;
}

.score-value {
    font-size: 1.4rem;
    font-weight: bold;
    color: #1a2a6c;
}

/* Modal styles */
.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.7);
    background: rgba(255, 255, 255, 0.98);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    text-align: center;
    width: 90%;
    max-width: 500px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease, transform 0.3s ease;
    z-index: 1000;
    pointer-events: auto !important;
}

.modal.active {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
    visibility: visible;
}

.modal input,
.modal button {
    position: relative;
    z-index: 1001;
    pointer-events: auto !important;
}

.modal.game-over {
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    0% { transform: translate(-50%, -150%); opacity: 0; }
    100% { transform: translate(-50%, -50%); opacity: 1; }
}

.modal h2 {
    color: #1a2a6c;
    margin-bottom: 20px;
}

/* Remove these properties as they were causing the issue */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.1);
    visibility: hidden;
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 999;
    pointer-events: auto;
}

.modal-backdrop.active {
    visibility: visible;
    opacity: 1;
    pointer-events: all; /* Re-enable pointer events only when active */
}

.modal input {
    pointer-events: all !important; /* Ensure inputs are always clickable */
    z-index: 1001; /* Place above backdrop */
}

/* Animations */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-20px); }
    60% { transform: translateY(-10px); }
}

.correct-answer {
    animation: bounce 1s ease;
}

.teacher-section {
    margin: 20px 0;
}

.word-input-group {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin: 20px 0;
}

.word-input-group input {
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1.1rem;
    width: 100%;
}

.word-input-group input:focus {
    outline: none;
    border-color: #1a2a6c;
    box-shadow: 0 0 10px rgba(26, 42, 108, 0.2);
}

.custom-word-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 10px;
    margin-top: 10px;
}

.word-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px;
    border-bottom: 1px solid #eee;
}

.word-item button {
    background: #ff4444;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 0.8rem;
}

.shake {
    animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
}

@keyframes shake {
    10%, 90% { transform: translate3d(-1px, 0, 0); }
    20%, 80% { transform: translate3d(2px, 0, 0); }
    30%, 50%, 70% { transform: translate3d(-4px, 0, 0); }
    40%, 60% { transform: translate3d(4px, 0, 0); }
}

.confetti {
    position: fixed;
    top: 0;
    left: 50%;
    width: 10px;
    height: 10px;
    background: #ff0;
    opacity: 0.8;
    transform-origin: left bottom;
    animation: confetti 5s ease-in-out infinite;
    z-index: 1000;
}

@keyframes confetti {
    0% { transform: translateY(0) rotate(0); opacity: 1; }
    100% { transform: translateY(100vh) rotate(720deg); opacity: 0; }
}

.confetti-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
}

/* Congratulatory modal */
.congrats-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    text-align: center;
    width: 80%;
    max-width: 400px;
    z-index: 1001;
    display: none;
}

.congrats-modal.active {
    display: block;
}

.congrats-modal h2 {
    color: #1a2a6c;
    margin-bottom: 20px;
}

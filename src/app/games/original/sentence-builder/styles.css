* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #1a2a6c, #b21f1f, #fdbb2d);
    color: #333;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    animation: gradientBG 15s ease infinite;
    background-size: 400% 400%;
}

@keyframes gradientBG {
    0% { background-position: 0% 50% }
    50% { background-position: 100% 50% }
    100% { background-position: 0% 50% }
}

.game-wrapper {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    width: 95%;
    max-width: 1200px;
    backdrop-filter: blur(10px);
    transform: translateY(0);
    transition: transform 0.3s ease;
}

.game-wrapper:hover {
    transform: translateY(-5px);
}

.header {
    text-align: center;
    margin-bottom: 30px;
}

.title {
    font-size: 2.8rem;
    margin-bottom: 30px;
    color: #1a2a6c;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.sentence-input-section {
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
    text-align: center;
}

.sentence-inputs {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;
    justify-content: center;
}

.sentence-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
    margin: 20px 0;
}

.sentence-item {
    background: rgba(26, 42, 108, 0.1);
    padding: 10px;
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sentence-grid {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px;
    margin: 20px 0;
    min-height: 150px;
    padding: 20px;
    background: rgba(26, 42, 108, 0.1);
    border-radius: 15px;
}

.sentence-card {
    background: rgba(255, 255, 255, 0.98);
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    padding: 15px 25px;
    cursor: move;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    user-select: none;
    font-size: 1.5rem; /* Increase font size */
}

.sentence-card:hover {
    transform: translateY(-5px);
}

.sentence-card.correct {
    animation: celebrate 0.5s ease, bounce 1s ease;
    background: linear-gradient(135deg, #86efac, #d4f8db);
}


@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-20px); }
    60% { transform: translateY(-10px); }
}

@keyframes shake {
    10%, 90% { transform: translate3d(-1px, 0, 0); }
    20%, 80% { transform: translate3d(2px, 0, 0); }
    30%, 50%, 70% { transform: translate3d(-4px, 0, 0); }
    40%, 60% { transform: translate3d(4px, 0, 0); }
}

@keyframes celebrate {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.correct {
    animation: celebrate 0.5s ease, bounce 1s ease;
    background: linear-gradient(135deg, #86efac, #d4f8db);
}

.stats-bar {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 20px;
    background: rgba(26, 42, 108, 0.1);
    padding: 20px;
    border-radius: 15px;
    margin: 25px 0;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1.2rem;
    font-weight: bold;
}

.btn {
    padding: 12px 25px;
    border: none;
    border-radius: 10px;
    background: linear-gradient(135deg, #1a2a6c, #b21f1f);
    color: white;
    font-size: 1.1rem;
    cursor: pointer;
    margin: 8px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

textarea {
    width: 100%;
    height: 100px;
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 10px;
    font-size: 1rem;
    resize: none;
}

.leaderboard {
    margin-top: 30px;
    text-align: center;
}

.leaderboard-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.leaderboard-item {
    background: rgba(255, 255, 255, 0.9);
    padding: 10px;
    border-radius: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.completion-celebration {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    font-size: 2rem;
    z-index: 1000;
    flex-direction: column;
}

.completion-celebration button {
    margin-top: 20px;
    padding: 10px 20px;
    font-size: 1rem;
    background: #1a2a6c;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.completion-celebration button:hover {
    background: #b21f1f;
}

.notification {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    z-index: 2000;
    animation: fadeInOut 3s ease-in-out;
}

@keyframes fadeInOut {
    0% { opacity: 0; transform: translate(-50%, -20px); }
    15% { opacity: 1; transform: translate(-50%, 0); }
    85% { opacity: 1; transform: translate(-50%, 0); }
    100% { opacity: 0; transform: translate(-50%, -20px); }
}

.button-group {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
}

.share-btn {
    background: linear-gradient(135deg, #4CAF50, #45a049);
}

.share-btn:hover {
    background: linear-gradient(135deg, #45a049, #4CAF50);
}

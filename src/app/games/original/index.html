<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Language Games Hub | Learn Through Play</title>
    <!-- Base styles -->
    <link rel="stylesheet" href="../../styles.css">
    <!-- Component styles -->
    <link rel="stylesheet" href="../../styles/menu.css">
    <link rel="stylesheet" href="../../styles/play.css">
    <link rel="stylesheet" href="../../styles/auth-modal.css">
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script src="../../scripts/load-menu.js" defer></script>
    <style>
        /* Enhanced styles for the games page */
        .play-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        /* Hero Section */
        .games-hero {
            background: linear-gradient(135deg, #6366F1, #8B5CF6);
            border-radius: 20px;
            padding: 3rem 2rem;
            margin-bottom: 3rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(99, 102, 241, 0.2);
        }
        
        .games-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('https://images.unsplash.com/photo-1600456899121-68eda5705257?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80') center/cover;
            opacity: 0.1;
            mix-blend-mode: overlay;
        }
        
        .games-hero h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: white;
            position: relative;
        }
        
        .games-hero p {
            font-size: 1.25rem;
            color: rgba(255, 255, 255, 0.9);
            max-width: 600px;
            margin: 0 auto;
            position: relative;
        }
        
        /* Games Grid */
        .games-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }
        
        .game-item {
            height: 100%;
        }
        
        .game-card {
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 2rem 1.5rem;
            border-radius: 16px;
            text-decoration: none;
            color: white;
            text-align: center;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            background-size: 200% 200%;
            background-position: 0% 0%;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .game-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
            background-position: 100% 100%;
        }
        
        .game-icon {
            font-size: 3.5rem;
            margin-bottom: 1.5rem;
            width: 90px;
            height: 90px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transition: transform 0.3s ease;
        }
        
        .game-card:hover .game-icon {
            transform: scale(1.1);
        }
        
        .game-card h3 {
            margin: 0 0 0.75rem;
            font-size: 1.75rem;
            font-weight: 600;
        }
        
        .game-card p {
            font-size: 1rem;
            opacity: 0.9;
            margin: 0;
        }
        
        /* Game card color variations */
        .game-item:nth-child(1) .game-card { background: linear-gradient(135deg, #ff7e5f, #feb47b); }
        .game-item:nth-child(2) .game-card { background: linear-gradient(135deg, #6a11cb, #2575fc); }
        .game-item:nth-child(3) .game-card { background: linear-gradient(135deg, #ff9a9e, #fad0c4); color: #444; }
        .game-item:nth-child(4) .game-card { background: linear-gradient(135deg, #00b09b, #96c93d); }
        .game-item:nth-child(5) .game-card { background: linear-gradient(135deg, #f093fb, #f5576c); }
        .game-item:nth-child(6) .game-card { background: linear-gradient(135deg, #5ee7df, #b490ca); color: #444; }
        .game-item:nth-child(7) .game-card { background: linear-gradient(135deg, #43cea2, #185a9d); }
        .game-item:nth-child(8) .game-card { background: linear-gradient(135deg, #c471f5, #fa71cd); }
        .game-item:nth-child(9) .game-card { background: linear-gradient(135deg, #f83600, #f9d423); }
        
        /* Achievements Section */
        .achievements-section {
            background: white;
            border-radius: 16px;
            padding: 2.5rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        .achievements-section h2 {
            font-size: 1.8rem;
            margin-bottom: 1.5rem;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .achievements-section h2 i {
            color: gold;
        }
        
        .achievements-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1rem;
        }
        
        .achievement-item {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1.25rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: transform 0.2s ease;
        }
        
        .achievement-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
        }
        
        .achievement-icon {
            font-size: 1.5rem;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #e9ecef;
            border-radius: 50%;
            color: #6366F1;
        }
        
        .achievement-info h3 {
            margin: 0 0 0.25rem;
            font-size: 1rem;
            color: #333;
        }
        
        .achievement-info p {
            margin: 0;
            font-size: 0.85rem;
            color: #6c757d;
        }
        
        .empty-state {
            grid-column: 1 / -1;
            text-align: center;
            padding: 2rem;
            color: #6c757d;
            background: #f8f9fa;
            border-radius: 12px;
            border: 2px dashed #dee2e6;
        }
        
        @media (max-width: 768px) {
            .games-hero h1 {
                font-size: 2.25rem;
            }
            
            .games-hero p {
                font-size: 1rem;
            }
            
            .games-grid {
                grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
            }
            
            .achievements-section {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div id="menu-container"></div>

    <main class="main-content">
        <div class="play-container">
            <section class="games-hero">
                <h1>Play & Learn</h1>
                <p>Master languages through fun and engaging games designed to boost your vocabulary, grammar, and comprehension skills</p>
            </section>

            <div class="games-grid">
                <div class="game-item">
                    <a href="hangman/index.html" class="game-card">
                        <div class="game-icon">🎯</div>
                        <h3>Hangman</h3>
                        <p>Choose the correct letters to complete words</p>
                    </a>
                </div>
                
                <div class="game-item">
                    <a href="fruit-game/index.html" class="game-card">
                        <div class="game-icon">🔤</div>
                        <h3>Word Match</h3>
                        <p>Match words with their translations</p>
                    </a>
                </div>

                <div class="game-item">
                    <a href="emoji-game/index.html" class="game-card">
                        <div class="game-icon">😀</div>
                        <h3>Emoji Match</h3>
                        <p>Learn vocabulary with emoji associations</p>
                    </a>
                </div>

                <div class="game-item">
                    <a href="sentence-builder/index.html" class="game-card">
                        <div class="game-icon">📝</div>
                        <h3>Sentence Builder</h3>
                        <p>Construct proper sentences from words</p>
                    </a>
                </div>

                <div class="game-item">
                    <a href="verb-quest/index.html" class="game-card">
                        <div class="game-icon">⚔️</div>
                        <h3>Verb Quest</h3>
                        <p>Practice verb conjugations in context</p>
                    </a>
                </div>

                <div class="game-item">
                    <a href="fill-lyrics/index.html" class="game-card">
                        <div class="game-icon">🎵</div>
                        <h3>Fill the Lyrics</h3>
                        <p>Complete song lyrics while listening</p>
                    </a>
                </div>

                <div class="game-item">
                    <a href="memory-game/index.html" class="game-card">
                        <div class="game-icon">🧠</div>
                        <h3>Memory Match</h3>
                        <p>Find matching pairs of words and images</p>
                    </a>
                </div>

                <div class="game-item">
                    <a href="word-scramble/index.html" class="game-card">
                        <div class="game-icon">🔤</div>
                        <h3>Word Scramble</h3>
                        <p>Unscramble words against the clock</p>
                    </a>
                </div>

                <div class="game-item">
                    <a href="noughts-and-crosses/index.html" class="game-card">
                        <div class="game-icon">⭕</div>
                        <h3>Word Tic-Tac-Toe</h3>
                        <p>Play tic-tac-toe with language challenges</p>
                    </a>
                </div>
            </div>

            <section class="achievements-section">
                <h2><i class="fas fa-trophy"></i> Recent Achievements</h2>
                <div id="recentAchievements" class="achievements-list">
                    <!-- Achievements will be populated dynamically -->
                    <div class="empty-state">
                        <i class="fas fa-medal" style="font-size: 2rem; display: block; margin-bottom: 1rem; color: #adb5bd;"></i>
                        <p>Play games to earn achievements and track your progress!</p>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <script src="../../scripts/achievement-system.js"></script>
    <script src="../../scripts/game-state.js"></script>
</body>
</html>
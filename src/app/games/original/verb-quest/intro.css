#gameScreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #1a1a1a;
    z-index: 0;
}

.game-nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 50px;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: space-around;
    align-items: center;
    z-index: 100;
}

.game-nav button {
    padding: 8px 16px;
    background: #ffd700;
    color: #000;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.game-nav button:hover {
    background: #ffed4a;
    transform: translateY(-2px);
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #1a1a1a;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-content {
    text-align: center;
    color: #fff;
}

.loading-content h1 {
    font-size: 3em;
    margin-bottom: 20px;
    color: #ffd700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.loading-bar {
    width: 300px;
    height: 20px;
    background: #333;
    border-radius: 10px;
    overflow: hidden;
    margin: 20px auto;
}

.loading-progress {
    width: 0%;
    height: 100%;
    background: linear-gradient(90deg, #ffd700, #ff9900);
    transition: width 0.3s ease;
}

.loading-tip {
    font-size: 1.1em;
    color: #ccc;
    margin-top: 20px;
}

/* Title Screen */
.title-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 50%, #1a1a1a 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.title-content {
    text-align: center;
    color: #fff;
}

.title-content h1 {
    font-size: 4em;
    margin-bottom: 30px;
    color: #ffd700;
    text-shadow: 0 0 15px rgba(255, 215, 0, 0.7);
}

.title-buttons {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.title-btn {
    padding: 12px 24px;
    font-size: 1.2em;
    background: #ffd700;
    color: #000;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.title-btn:hover {
    background: #ffed4a;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Cutscene */
.cutscene {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    transition: background-image 1s ease;
    z-index: 1000;
}

.cutscene-content {
    position: absolute;
    bottom: 50px;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    max-width: 800px;
    background: rgba(0, 0, 0, 0.8);
    padding: 20px;
    border-radius: 10px;
    border: 2px solid #ffd700;
}

.cutscene-text {
    color: #fff;
    font-size: 1.5em;
    text-align: center;
    line-height: 1.5;
    margin-bottom: 20px;
}

.skip-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    padding: 10px 20px;
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
    border: 1px solid #fff;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.skip-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Character Creation Screen */
.character-creation-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.character-creation-overlay h2 {
    color: #ffd700;
    font-size: 2.5em;
    margin-bottom: 30px;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.character-form-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
    max-width: 400px;
}

.character-form-container input,
.character-form-container select {
    padding: 12px;
    font-size: 1.1em;
    border: 2px solid #ffd700;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border-radius: 5px;
}

.character-form-container input:focus,
.character-form-container select:focus {
    outline: none;
    border-color: #ff9900;
    box-shadow: 0 0 10px rgba(255, 153, 0, 0.5);
}

.character-form-container button {
    padding: 15px 30px;
    font-size: 1.2em;
    background: linear-gradient(45deg, #ffd700, #ff9900);
    color: black;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.character-form-container button:hover {
    transform: scale(1.05);
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.5);
}

/* Hide UI elements during intro */
body.intro-active .game-nav,
body.intro-active #questLog {
    display: none !important;
}

/* Menu Styling */
.menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 3000;
}

.menu-overlay.visible {
    display: flex;
}

.menu-content {
    background: rgba(0, 0, 0, 0.9);
    padding: 30px;
    border-radius: 15px;
    border: 2px solid #ffd700;
    min-width: 300px;
}

.menu-content h2 {
    color: #ffd700;
    font-size: 2em;
    margin-bottom: 20px;
    text-align: center;
}

.menu-buttons {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.menu-btn {
    padding: 12px 24px;
    font-size: 1.1em;
    background: linear-gradient(45deg, #4a4a4a, #2a2a2a);
    color: #fff;
    border: 2px solid #ffd700;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.menu-btn:hover {
    background: linear-gradient(45deg, #ffd700, #ff9900);
    color: #000;
    transform: scale(1.05);
} 
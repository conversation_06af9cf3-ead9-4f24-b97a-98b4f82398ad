/* CSS Variables */
:root {
    /* Colors */
    --primary-color: #1a2a6c;
    --secondary-color: #b21f1f;
    --accent-color: #ffd700;
    --text-color: #ffffff;
    --text-secondary: #cccccc;
    --background-color: #1a1a1a;
    --overlay-color: rgba(0, 0, 0, 0.8);
    
    /* Gradients */
    --primary-gradient: linear-gradient(45deg, #4a4a4a, #2a2a2a);
    --accent-gradient: linear-gradient(45deg, #ffd700, #ff9900);
    --danger-gradient: linear-gradient(45deg, #ff4d4d, #ff0000);
    --success-gradient: linear-gradient(45deg, #4CAF50, #45a049);
    
    /* Shadows */
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    --glow-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
    
    /* Spacing */
    --spacing-xs: 5px;
    --spacing-sm: 10px;
    --spacing-md: 20px;
    --spacing-lg: 30px;
    --spacing-xl: 40px;
    
    /* Border Radius */
    --border-radius-sm: 5px;
    --border-radius-md: 10px;
    --border-radius-lg: 15px;
    --border-radius-full: 50%;
    
    /* Z-index layers */
    --z-background: -1;
    --z-default: 1;
    --z-overlay: 100;
    --z-modal: 1000;
    --z-tooltip: 1500;
    --z-loading: 9999;
}

/* Reset */
*, *::before, *::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    width: 100%;
    overflow: hidden;
}

body {
    font-family: 'Arial', sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Game Container */
.game-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

/* Game Screen */
.game-screen {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

/* Navigation Bar */
.game-nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 60px;
    background: var(--overlay-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 var(--spacing-md);
    z-index: var(--z-overlay);
}

.nav-left, .nav-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

/* Player Stats */
.player-stats {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.stat {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.stat-icon {
    width: 24px;
    height: 24px;
}

.stat-value {
    font-weight: bold;
}

.health { color: #ff4d4d; }
.mana { color: #4d4dff; }
.gold { color: var(--accent-color); }
.xp { color: #4CAF50; }

/* Buttons */
.btn {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--primary-gradient);
    color: var(--text-color);
    border: 2px solid var(--accent-color);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
}

.btn:hover {
    background: var(--accent-gradient);
    color: black;
    transform: scale(1.05);
}

.btn:disabled {
    background: #333;
    border-color: #666;
    color: #888;
    cursor: not-allowed;
    transform: none;
}

.btn-primary {
    background: var(--primary-gradient);
}

.btn-accent {
    background: var(--accent-gradient);
    color: black;
}

.btn-danger {
    background: var(--danger-gradient);
}

.btn-success {
    background: var(--success-gradient);
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

.focus-visible:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #000080;
        --secondary-color: #800000;
        --accent-color: #ffff00;
        --text-color: #ffffff;
        --background-color: #000000;
        --overlay-color: rgba(0, 0, 0, 0.9);
    }

    .btn {
        border-width: 3px;
    }

    .game-nav {
        background: black;
        border-bottom: 2px solid var(--accent-color);
    }
}

/* Responsive Design */
@media screen and (max-width: 768px) {
    .game-nav {
        height: 50px;
        padding: 0 var(--spacing-sm);
    }

    .player-stats {
        gap: var(--spacing-sm);
    }

    .stat-icon {
        width: 20px;
        height: 20px;
    }

    .btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 14px;
    }
}

@media screen and (max-width: 480px) {
    .player-stats {
        flex-wrap: wrap;
    }

    .nav-left, .nav-right {
        gap: var(--spacing-xs);
    }

    .stat {
        font-size: 12px;
    }
} 
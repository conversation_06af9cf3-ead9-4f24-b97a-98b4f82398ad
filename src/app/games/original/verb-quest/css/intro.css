/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: linear-gradient(45deg, #1a1a1a, #2d2d2d);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-content {
    text-align: center;
    color: white;
    padding: 2rem;
}

.loading-content h1 {
    font-family: 'Cinzel', serif;
    font-size: 3.5rem;
    color: #ffd700;
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
    margin-bottom: 2rem;
}

.loading-bar {
    width: 300px;
    height: 10px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
    margin: 2rem auto;
    overflow: hidden;
}

.loading-progress {
    width: 0%;
    height: 100%;
    background: linear-gradient(90deg, #ffd700, #ff9900);
    border-radius: 5px;
    transition: width 0.3s ease;
}

.loading-tip {
    font-family: 'Poppins', sans-serif;
    font-size: 1.2rem;
    color: #ccc;
    margin-top: 2rem;
    font-style: italic;
}

/* Title Screen */
.title-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: url('../assets/backgrounds/title_background.jpg') no-repeat center center;
    background-size: cover;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.title-content {
    text-align: center;
    padding: 2rem;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 15px;
    border: 2px solid #ffd700;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.5);
}

.title-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 2rem;
}

.title-btn {
    padding: 1rem 2rem;
    font-size: 1.2rem;
    background: linear-gradient(45deg, #2a2a2a, #1a1a1a);
    color: #ffd700;
    border: 2px solid #ffd700;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Poppins', sans-serif;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.title-btn:hover {
    background: linear-gradient(45deg, #ffd700, #ff9900);
    color: black;
    transform: scale(1.05);
}

/* Cutscene */
.cutscene {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    z-index: 1000;
}

.cutscene-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 32px;
    font-weight: bold;
    color: white;
    text-shadow: 3px 3px 0 #000,
                -3px -3px 0 #000,
                3px -3px 0 #000,
                -3px 3px 0 #000,
                0 3px 0 #000,
                3px 0 0 #000,
                0 -3px 0 #000,
                -3px 0 0 #000;
    margin: 0;
    max-width: 800px;
    width: 90%;
    text-align: center;
    line-height: 1.4;
    padding: 2rem;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 15px;
    animation: fadeIn 1s ease-in-out;
}

.skip-btn {
    position: absolute;
    bottom: 2rem;
    right: 2rem;
    padding: 0.8rem 1.5rem;
    background: rgba(0, 0, 0, 0.8);
    color: #ffd700;
    border: 2px solid #ffd700;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Poppins', sans-serif;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.skip-btn:hover {
    background: rgba(255, 215, 0, 0.2);
    transform: scale(1.05);
}

/* Character Creation */
.character-creation-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.9)), url('../assets/backgrounds/character_creation.jpg') no-repeat center center;
    background-size: cover;
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.character-creation-overlay.active {
    display: flex;
}

.character-creation-content {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid #ffd700;
    border-radius: 15px;
    padding: 2rem;
    width: 90%;
    max-width: 800px;
    color: white;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.5);
}

.character-creation-content h2 {
    font-family: 'Cinzel', serif;
    font-size: 2.5rem;
    color: #ffd700;
    text-align: center;
    margin-bottom: 2rem;
    text-shadow: 0 0 15px rgba(255, 215, 0, 0.5);
}

.character-form {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.form-group {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.form-group label {
    font-family: 'Poppins', sans-serif;
    font-size: 1.2rem;
    color: #ffd700;
    min-width: 150px;
}

.character-name-input {
    padding: 0.75rem;
    font-size: 1rem;
    background: rgba(0, 0, 0, 0.5);
    border: 2px solid #ffd700;
    border-radius: 8px;
    color: white;
    width: 250px;
}

.class-selection {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.class-option {
    background: rgba(0, 0, 0, 0.5);
    border: 2px solid #ffd700;
    border-radius: 10px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.class-option:hover {
    background: rgba(255, 215, 0, 0.1);
    transform: translateY(-5px);
}

.class-option.selected {
    background: rgba(255, 215, 0, 0.2);
    border-color: #ff9900;
    transform: translateY(-5px);
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.class-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.class-name {
    font-family: 'Cinzel', serif;
    font-size: 1.5rem;
    color: #ffd700;
    margin-bottom: 0.5rem;
}

.class-description {
    font-size: 0.9rem;
    color: #ccc;
    line-height: 1.4;
}

#beginAdventure {
    padding: 1rem 2rem;
    font-size: 1.2rem;
    background: linear-gradient(45deg, #ffd700, #ff9900);
    color: black;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Poppins', sans-serif;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-top: 2rem;
    width: 100%;
    font-weight: bold;
}

#beginAdventure:hover {
    transform: scale(1.02);
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

/* Settings Overlay */
.settings-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1100;
}

.settings-content {
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid #ffd700;
    border-radius: 15px;
    padding: 2rem;
    width: 90%;
    max-width: 500px;
    color: white;
}

.settings-content h2 {
    font-family: 'Cinzel', serif;
    font-size: 2rem;
    color: #ffd700;
    text-align: center;
    margin-bottom: 2rem;
}

.settings-options {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
}

.setting-btn {
    padding: 0.5rem 1rem;
    font-size: 1.2rem;
    background: none;
    border: 2px solid #ffd700;
    border-radius: 5px;
    color: #ffd700;
    cursor: pointer;
    transition: all 0.3s ease;
}

.setting-btn:hover {
    background: rgba(255, 215, 0, 0.2);
}

.close-btn {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    background: linear-gradient(45deg, #2a2a2a, #1a1a1a);
    color: #ffd700;
    border: 2px solid #ffd700;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 2rem;
    width: 100%;
}

.close-btn:hover {
    background: linear-gradient(45deg, #ffd700, #ff9900);
    color: black;
}

/* Credits Overlay */
.credits-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1100;
}

.credits-content {
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid #ffd700;
    border-radius: 15px;
    padding: 2rem;
    width: 90%;
    max-width: 500px;
    color: white;
    text-align: center;
}

.credits-content h2 {
    font-family: 'Cinzel', serif;
    font-size: 2rem;
    color: #ffd700;
    margin-bottom: 2rem;
}

.credits-text {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 2rem;
}

/* Animations */
@keyframes titleGlow {
    0%, 100% { text-shadow: 0 0 20px rgba(255, 215, 0, 0.5); }
    50% { text-shadow: 0 0 40px rgba(255, 215, 0, 0.8); }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
} 
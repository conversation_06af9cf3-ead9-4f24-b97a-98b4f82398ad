/* Menu Overlay */
.menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.menu-content {
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid #ffd700;
    border-radius: 10px;
    padding: 30px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    min-width: 300px;
}

.menu-btn {
    padding: 12px 24px;
    background: linear-gradient(45deg, #4a4a4a, #2a2a2a);
    color: white;
    border: 2px solid #ffd700;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
}

.menu-btn:hover {
    background: linear-gradient(45deg, #ffd700, #ff9900);
    color: black;
    transform: scale(1.05);
}

/* Dialog System Styles */
.dialog-container {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    padding: 20px;
    z-index: 1000;
    pointer-events: none;
}

.dialog-box {
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid gold;
    border-radius: 10px;
    padding: 20px;
    width: 80%;
    max-width: 800px;
    display: grid;
    grid-template-columns: 120px 1fr;
    grid-gap: 20px;
    pointer-events: auto;
    animation: slideUp 0.3s ease-out;
}

.npc-portrait {
    width: 120px;
    height: 120px;
    border-radius: 10px;
    background-size: cover;
    background-position: center;
    border: 2px solid gold;
}

.dialog-text {
    color: white;
}

.dialog-text h3 {
    color: gold;
    margin: 0 0 10px 0;
    font-size: 1.5em;
}

.dialog-text p {
    margin: 0;
    line-height: 1.5;
    font-size: 1.1em;
}

.dialog-choices {
    grid-column: 1 / -1;
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 20px;
}

.dialog-choice {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 5px;
    color: white;
    padding: 10px 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
}

.dialog-choice:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: gold;
}



/* Notification Styles */
.notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid #4CAF50;
    border-radius: 5px;
    color: white;
    padding: 15px 20px;
    animation: slideIn 0.3s ease-out, fadeOut 0.3s ease-out 2.7s forwards;
    z-index: 1002;
}

.notification.error {
    border-color: #f44336;
}

.notification.warning {
    border-color: #ff9800;
}

.notification.success {
    border-color: #4CAF50;
}

/* Shop Item Styles */
.shop-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 10px;
}

.shop-item span {
    color: gold;
    font-size: 1.1em;
    display: block;
    margin-bottom: 5px;
}

.shop-item p {
    color: #ccc;
    margin: 5px 0 10px 0;
}

.shop-item button {
    background: #1a472a;
    color: white;
    border: none;
    padding: 5px 15px;
    border-radius: 3px;
    cursor: pointer;
}

.shop-item button:hover {
    background: #2a673a;
}

/* Dev Panel Styles */
.dev-panel {
    position: fixed;
    top: 60px;
    right: 10px;
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid #666;
    border-radius: 8px;
    padding: 15px;
    z-index: 9999;
    color: #fff;
    font-family: monospace;
    min-width: 250px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

.dev-panel.visible {
    display: block !important;
}

.dev-panel h3 {
    margin: 0 0 10px 0;
    padding-bottom: 5px;
    border-bottom: 1px solid #666;
}

.dev-panel-section {
    margin: 10px 0;
}

.dev-stats {
    font-size: 12px;
    line-height: 1.4;
    margin-bottom: 10px;
    padding: 5px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.dev-btn {
    display: block;
    width: 100%;
    padding: 8px;
    margin: 5px 0;
    background: #333;
    border: 1px solid #555;
    border-radius: 4px;
    color: #fff;
    cursor: pointer;
    font-size: 12px;
    text-align: left;
    transition: all 0.2s ease;
}

.dev-btn:hover {
    background: #444;
    border-color: #666;
}

.dev-btn.success {
    background: #1a472a;
    border-color: #2a673a;
}

.dev-btn.success:hover {
    background: #2a673a;
    border-color: #3a774a;
}

.dev-btn.danger {
    background: #8b0000;
    border-color: #a00000;
}

.dev-btn.danger:hover {
    background: #a00000;
    border-color: #b00000;
}

#debug-overlay {
    position: fixed;
    top: 10px;
    left: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: #00ff00;
    padding: 10px;
    border-radius: 5px;
    font-family: monospace;
    font-size: 12px;
    z-index: 9999;
    pointer-events: none;
    line-height: 1.4;
}

/* Achievement Notification */
.achievement {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid var(--accent-color);
    border-radius: 10px;
    padding: 15px 30px;
    color: white;
    text-align: center;
    animation: achievementSlide 4s ease-in-out;
    z-index: 2000;
}

.achievement h4 {
    color: var(--accent-color);
    margin: 0 0 5px 0;
}

.achievement p {
    margin: 0;
    font-size: 0.9em;
}

/* Game Message */
.game-message {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    z-index: 1000;
    display: none;
}

/* Animations */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes achievementSlide {
    0% { transform: translate(-50%, -100%); }
    15% { transform: translate(-50%, 0); }
    85% { transform: translate(-50%, 0); }
    100% { transform: translate(-50%, -100%); }
} 
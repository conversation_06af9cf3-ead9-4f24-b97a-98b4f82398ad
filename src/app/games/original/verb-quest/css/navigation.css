.game-nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 60px;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    z-index: 1000;
    border-bottom: 2px solid var(--accent-color);
}

.game-nav .player-info {
    display: flex;
    align-items: center;
    gap: 20px;
    color: white;
}

.game-nav .hp-bar,
.game-nav .xp-bar {
    width: 200px;
    height: 20px;
    background: rgba(0, 0, 0, 0.5);
    border: 2px solid #ffd700;
    border-radius: 10px;
    overflow: hidden;
}

.game-nav .hp-fill {
    height: 100%;
    background: linear-gradient(90deg, #ff0000, #ff4d4d);
    transition: width 0.3s ease;
}

.game-nav .xp-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #8BC34A);
    transition: width 0.3s ease;
}

.game-nav .controls {
    display: flex;
    gap: 10px;
}

.game-nav button {
    padding: 8px 16px;
    background: linear-gradient(45deg, #4a4a4a, #2a2a2a);
    color: white;
    border: 2px solid #ffd700;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.game-nav button:hover {
    background: linear-gradient(45deg, #ffd700, #ff9900);
    color: black;
}

.player-stats {
    display: flex;
    gap: 20px;
    align-items: center;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #fff;
    font-size: 1rem;
}

.stat-label {
    color: var(--accent-color);
    font-weight: bold;
}

.xp-bar {
    width: 100px;
    height: 10px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 5px;
    overflow: hidden;
    margin: 0 10px;
}

.xp-bar-fill {
    width: 0%;
    height: 100%;
    background: linear-gradient(90deg, #4a00e0, #8e2de2);
    transition: width 0.3s ease;
}

.game-controls {
    display: flex;
    gap: 10px;
}

.game-controls button {
    padding: 8px 15px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--accent-color);
    border-radius: 5px;
    color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
}

.game-controls button:hover {
    background: var(--accent-color);
    transform: scale(1.05);
}

#soundToggleBtn {
    font-size: 1.2rem;
    padding: 5px 10px;
}

.home-btn {
    color: white;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.menu-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    transition: transform 0.2s ease;
}

.menu-btn:hover {
    transform: scale(1.1);
}

.fullscreen-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    transition: transform 0.2s ease;
}

.fullscreen-btn:hover {
    transform: scale(1.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .game-nav {
        height: auto;
        padding: 10px;
        flex-direction: column;
        gap: 10px;
    }

    .player-stats {
        flex-wrap: wrap;
        justify-content: center;
    }

    .game-controls {
        flex-wrap: wrap;
        justify-content: center;
    }
} 
.dev-controls {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 10px;
    background: rgba(0, 0, 0, 0.8);
    padding: 10px;
    border-radius: 8px;
}

.dev-skip-btn, .dev-btn {
    padding: 8px 16px;
    border: 2px solid;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    font-size: 12px;
    text-transform: uppercase;
    transition: all 0.2s ease;
}

.dev-skip-btn {
    background-color: #ff4444;
    color: white;
    border-color: #cc0000;
}

.dev-btn {
    background-color: #444;
    color: #fff;
    border-color: #666;
}

.dev-skip-btn:hover {
    background-color: #ff6666;
    transform: translateY(-1px);
}

.dev-btn:hover {
    background-color: #666;
    transform: translateY(-1px);
}

#debug-overlay {
    font-size: 12px;
    line-height: 1.4;
    white-space: pre;
}

.battle-timer {
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    margin: 10px 0;
    padding: 5px;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

.battle-screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.health-bars {
    position: fixed;
    top: calc(4rem + 20px);
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    padding: 0 50px;
    z-index: 10;
}

.player-health, .enemy-health {
    width: 300px;
    background: rgba(0, 0, 0, 0.7);
    padding: 10px;
    border-radius: 8px;
    color: white;
}

.health-bar {
    width: 100%;
    height: 20px;
    background: rgba(255, 0, 0, 0.3);
    border-radius: 10px;
    margin: 5px 0;
    overflow: hidden;
    border: 2px solid #ffd700;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.health-fill {
    height: 100%;
    background: linear-gradient(90deg, #ff4444, #ff0000);
    transition: width 0.3s ease;
}

.enemy-container {
    position: absolute;
    left: 0;
    top: calc(4rem + 60px);
    width: 50%;
    height: calc(100% - 4rem - 60px);
    display: flex;
    justify-content: center;
    align-items: center;
}

.enemy-sprite {
    width: 100%;
    height: 100%;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    animation: enemyIdle 3s infinite;
}

.conjugation-box {
    position: fixed;
    right: 5%;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.8);
    padding: 2rem;
    border-radius: 15px;
    color: white;
    width: 400px;
    text-align: center;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
    border: 2px solid #ffd700;
    z-index: 5;
}

.verb-challenge {
    margin-bottom: 20px;
}

.verb-challenge h3 {
    font-size: 24px;
    color: #ffd700;
    margin-bottom: 15px;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.verb-text {
    font-size: 28px;
    color: white;
    margin: 10px 0;
}

.pronoun-text, .tense-text {
    font-size: 18px;
    color: #ddd;
    margin: 5px 0;
}

.answer-form {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 15px;
}

#answerInput {
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    font-size: 18px;
    width: 250px;
    background: rgba(255, 255, 255, 0.9);
}

#answerInput:focus {
    outline: none;
    box-shadow: 0 0 0 2px #2ecc71;
}

.answer-form button {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    background: linear-gradient(45deg, #ffd700, #ff9900);
    color: black;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.answer-form button:hover {
    background: linear-gradient(45deg, #ff9900, #ffd700);
    transform: translateY(-2px);
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
}

.battle-timer {
    position: fixed;
    top: calc(4rem + 20px);
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.7);
    padding: 10px 20px;
    border-radius: 4px;
    color: white;
    font-size: 20px;
    z-index: 10;
}

.damage-number {
    position: absolute;
    font-size: 24px;
    font-weight: bold;
    animation: floatUp 1s ease-out forwards;
    z-index: 100;
}

.enemy-damage {
    color: #e74c3c;
}

.player-damage {
    color: #e74c3c;
}

@keyframes floatUp {
    0% {
        transform: translateY(0);
        opacity: 1;
    }
    100% {
        transform: translateY(-50px);
        opacity: 0;
    }
}

/* Enemy damage animation */
.enemy-sprite.damaged {
    animation: damageFlash 0.5s;
    filter: brightness(2) saturate(2);
}

@keyframes damageFlash {
    0% { filter: brightness(1) saturate(1); }
    50% { filter: brightness(2) saturate(2); }
    100% { filter: brightness(1) saturate(1); }
}

.damage-number {
    position: absolute;
    color: #ff0000;
    font-size: 24px;
    font-weight: bold;
    text-shadow: 2px 2px 0 #000;
    animation: floatUp 1s ease-out forwards;
    z-index: 1000;
}

.damage-number.player-damage {
    color: #ff4444;
}

@keyframes floatUp {
    0% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
    100% {
        transform: translateY(-50px) scale(1.5);
        opacity: 0;
    }
}

.screen-shake {
    animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
}

@keyframes shake {
    10%, 90% { transform: translate3d(-1px, 0, 0); }
    20%, 80% { transform: translate3d(2px, 0, 0); }
    30%, 50%, 70% { transform: translate3d(-4px, 0, 0); }
    40%, 60% { transform: translate3d(4px, 0, 0); }
}

/* Critical hit animation */
.critical-hit {
    animation: criticalDamage 0.8s;
}

@keyframes criticalDamage {
    0% { transform: scale(1); filter: brightness(1); }
    50% { transform: scale(1.2); filter: brightness(1.8); }
    100% { transform: scale(1); filter: brightness(1); }
}

/* Enemy idle animation */
.enemy-sprite {
    animation: enemyIdle 3s infinite;
}

@keyframes enemyIdle {
    0% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0); }
}

.enemy-info {
    position: absolute;
    top: 2rem;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    z-index: 2;
}

.enemy-name {
    font-size: 2rem;
    font-weight: bold;
    color: var(--accent-color);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    margin-bottom: 1rem;
}

.verb-display {
    text-align: center;
    margin-bottom: 20px;
}

.verb-display h3 {
    color: #ffd700;
    font-size: 1.8rem;
    margin-bottom: 15px;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.verb-info {
    margin-bottom: 20px;
}

.verb-infinitive {
    font-size: 2rem;
    color: #ffd700;
    display: block;
    margin-bottom: 10px;
}

.verb-details {
    font-size: 1.2rem;
    color: #ccc;
}

.verb-details p {
    margin: 5px 0;
}

.submit-btn {
    padding: 12px 24px;
    font-size: 1.1rem;
    background: linear-gradient(45deg, #ffd700, #ff9900);
    border: none;
    border-radius: 8px;
    color: black;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
}

.submit-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
}

.combo-counter {
    text-align: center;
    margin-top: 15px;
    font-size: 1.2rem;
    color: #ffd700;
}

/* Battle Effects */
.battle-effect {
    position: absolute;
    pointer-events: none;
    z-index: 100;
}

.hit-effect {
    animation: hitFlash 0.3s ease-out;
}

.heal-effect {
    animation: healPulse 0.5s ease-out;
}

.shield-effect {
    animation: shieldPulse 1s ease-out;
}

@keyframes hitFlash {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.8; }
    100% { transform: scale(1.5); opacity: 0; }
}

@keyframes healPulse {
    0% { transform: scale(0); opacity: 1; background: rgba(76, 175, 80, 0.5); }
    100% { transform: scale(2); opacity: 0; background: rgba(76, 175, 80, 0); }
}

@keyframes shieldPulse {
    0% { transform: scale(1); opacity: 1; border: 2px solid rgba(33, 150, 243, 0.8); }
    100% { transform: scale(1.5); opacity: 0; border: 2px solid rgba(33, 150, 243, 0); }
}

/* Enemy Status Effects */
.status-effect {
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.9em;
    padding: 5px 10px;
    border-radius: 5px;
    color: white;
    text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

.status-effect.poison {
    background: rgba(156, 39, 176, 0.8);
}

.status-effect.stun {
    background: rgba(255, 152, 0, 0.8);
}

.status-effect.weakness {
    background: rgba(244, 67, 54, 0.8);
}

/* Battle Result Styles */
.battle-result {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    border: 3px solid #ffd700;
    border-radius: 10px;
    padding: 30px;
    text-align: center;
    color: white;
    z-index: 1000;
    display: none;
}

.battle-result h2 {
    color: #ffd700;
    margin: 0 0 20px 0;
    font-size: 2em;
}

.battle-result p {
    margin: 10px 0;
    font-size: 1.2em;
}

.battle-result button {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid #ffd700;
    border-radius: 5px;
    color: white;
    padding: 10px 20px;
    margin: 10px;
    cursor: pointer;
    font-size: 1.1em;
    transition: all 0.2s ease;
}

.battle-result button:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.battle-result.victory {
    border-color: #4CAF50;
}

.battle-result.victory h2 {
    color: #4CAF50;
}

.battle-result.victory button {
    border-color: #4CAF50;
}

.battle-result.victory button:hover {
    background: rgba(76, 175, 80, 0.2);
}

.battle-result.defeat {
    border-color: #f44336;
}

.battle-result.defeat h2 {
    color: #f44336;
}

.battle-result.defeat button {
    border-color: #f44336;
}

.battle-result.defeat button:hover {
    background: rgba(244, 67, 54, 0.2);
}

.battle-result .retry-btn {
    background: rgba(244, 67, 54, 0.1);
}

.battle-result .continue-btn {
    background: rgba(76, 175, 80, 0.1);
}

.battle-result .retreat-btn {
    background: rgba(255, 152, 0, 0.1);
    border-color: #ff9800;
}

.battle-result .retreat-btn:hover {
    background: rgba(255, 152, 0, 0.2);
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -60%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

/* Victory/Defeat Screens */
.victory-screen {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    background: rgba(0, 0, 0, 0.95) !important;
    padding: 30px !important;
    border-radius: 15px !important;
    text-align: center !important;
    color: white !important;
    width: 80% !important;
    max-width: 500px !important;
    z-index: 1000 !important;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5) !important;
    border: 2px solid #ffd700 !important;
}

.victory-screen h2 {
    font-size: 2em !important;
    margin-bottom: 20px !important;
    color: #ffd700 !important;
}

.victory-screen p {
    margin: 10px 0 !important;
    font-size: 1.2em !important;
}

.victory-buttons {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    gap: 15px !important;
    margin-top: 25px !important;
}

.victory-buttons button {
    width: 200px !important;
    padding: 12px 20px !important;
    font-size: 1.1em !important;
    background: #4a90e2 !important;
    color: white !important;
    border: none !important;
    border-radius: 5px !important;
    cursor: pointer !important;
    transition: background 0.3s ease !important;
}

.victory-buttons button:hover {
    background: #357abd !important;
}

.defeat-screen {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    color: white;
    width: 80%;
    max-width: 500px;
    z-index: 1000;
}

.battle-rewards {
    margin: 20px 0;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
}

.battle-rewards h3 {
    color: #ffd700;
    margin: 0 0 10px 0;
}

.reward-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 5px 0;
    padding: 5px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
}

.combo-number {
    color: var(--accent-color);
    font-weight: bold;
    font-size: 1.4rem;
}

.battle-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.special-ability-btn,
.item-btn {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    background: linear-gradient(45deg, #4a00e0, #8e2de2);
    color: #fff;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.special-ability-btn:hover,
.item-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 0 15px rgba(142, 45, 226, 0.5);
}

.timer {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 1.2rem;
    color: #fff;
    background: rgba(0, 0, 0, 0.5);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    border: 2px solid var(--accent-color);
}

/* Special Ability Effects */
.special-ability {
    position: absolute;
    pointer-events: none;
    z-index: 1000;
}

.warrior-slash {
    width: 100px;
    height: 100px;
    background: linear-gradient(45deg, #ff4d4d, #ff0000);
    clip-path: polygon(0 0, 100% 0, 100% 20%, 0 20%);
    transform-origin: center;
    animation: slashEffect 0.3s ease-out forwards;
}

.mage-spell {
    width: 50px;
    height: 50px;
    background: radial-gradient(circle, #00ffff, #0000ff);
    border-radius: 50%;
    animation: spellEffect 0.5s ease-out forwards;
}

.rogue-stealth {
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    animation: stealthEffect 0.3s ease-in-out;
}

/* Ability Cooldown */
.ability-cooldown {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    border-radius: inherit;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    font-size: 1.2em;
    animation: cooldownProgress linear forwards;
}

/* Item Buttons */
.item-button {
    background: linear-gradient(45deg, #4a4a4a, #2a2a2a);
    border: 2px solid #ffd700;
    border-radius: 5px;
    color: white;
    padding: 10px 20px;
    margin: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.item-button:hover {
    background: linear-gradient(45deg, #ffd700, #ff9900);
    color: black;
    transform: scale(1.05);
}

.item-button:disabled {
    background: #333;
    border-color: #666;
    color: #888;
    cursor: not-allowed;
    transform: none;
}

/* Battle Timer */
.battle-timer {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 200px;
    height: 10px;
    background: #333;
    border: 2px solid #000;
    border-radius: 5px;
    overflow: hidden;
}

.timer-fill {
    height: 100%;
    background: linear-gradient(45deg, #ffd700, #ff9900);
    transition: width 0.1s linear;
}

.battle-rewards {
    margin: 20px 0;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
}

.battle-rewards h3 {
    color: #ffd700;
    margin: 0 0 10px 0;
}

.reward-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 5px 0;
    padding: 5px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
}

.combo-number {
    color: var(--accent-color);
    font-weight: bold;
    font-size: 1.4rem;
}

.battle-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.special-ability-btn,
.item-btn {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    background: linear-gradient(45deg, #4a00e0, #8e2de2);
    color: #fff;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.special-ability-btn:hover,
.item-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 0 15px rgba(142, 45, 226, 0.5);
}

.timer {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 1.2rem;
    color: #fff;
    background: rgba(0, 0, 0, 0.5);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    border: 2px solid var(--accent-color);
}

/* Menu Overlays */
.quest-log,
.inventory,
.settings-menu {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.95);
    border: 2px solid #ffd700;
    border-radius: 10px;
    padding: 20px;
    min-width: 300px;
    max-width: 80%;
    max-height: 80vh;
    overflow-y: auto;
    z-index: 1000;
    display: none;
    color: #fff;
}

.quest-log-content,
.inventory-content,
.settings-content {
    position: relative;
}

.close-button {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    color: #ffd700;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-button:hover {
    color: #fff;
}

.setting-item {
    margin: 15px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.toggle-btn {
    background: linear-gradient(45deg, #ffd700, #ff9900);
    border: none;
    padding: 5px 15px;
    border-radius: 15px;
    color: #000;
    cursor: pointer;
    font-weight: bold;
}

.toggle-btn:hover {
    filter: brightness(1.2);
}

#difficultySelect {
    background: #333;
    color: #fff;
    border: 1px solid #ffd700;
    padding: 5px 10px;
    border-radius: 5px;
}

.inventory-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    padding: 10px;
}

.quest-list {
    margin-top: 10px;
}

/* Cutscene styles */
.cutscene-text {
    font-size: 28px;
    font-weight: bold;
    color: white;
    text-shadow: 2px 2px 0 #000,
                -2px -2px 0 #000,
                2px -2px 0 #000,
                -2px 2px 0 #000;
    margin: 20px 0;
    text-align: center;
    line-height: 1.4;
}
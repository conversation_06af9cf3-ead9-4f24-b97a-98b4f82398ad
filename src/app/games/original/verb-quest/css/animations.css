/* Fade Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Slide Animations */
@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

@keyframes slideOut {
    from { transform: translateX(0); }
    to { transform: translateX(100%); }
}

@keyframes slideUp {
    from { transform: translateY(100%); }
    to { transform: translateY(0); }
}

@keyframes slideDown {
    from { transform: translateY(-100%); }
    to { transform: translateY(0); }
}

/* Scale Animations */
@keyframes scaleIn {
    from { transform: scale(0); }
    to { transform: scale(1); }
}

@keyframes scaleOut {
    from { transform: scale(1); }
    to { transform: scale(0); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

/* Rotate Animations */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes spinReverse {
    from { transform: rotate(360deg); }
    to { transform: rotate(0deg); }
}

/* Special Effect Animations */
@keyframes glow {
    0%, 100% { text-shadow: 0 0 20px rgba(255, 215, 0, 0.5); }
    50% { text-shadow: 0 0 40px rgba(255, 215, 0, 0.8); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

@keyframes float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

/* Battle Effect Animations */
@keyframes damageNumber {
    0% {
        transform: translateY(0);
        opacity: 1;
    }
    100% {
        transform: translateY(-50px);
        opacity: 0;
    }
}

@keyframes criticalHit {
    0% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
    50% {
        transform: scale(1.5) rotate(180deg);
        opacity: 0.8;
    }
    100% {
        transform: scale(0) rotate(360deg);
        opacity: 0;
    }
}

@keyframes healEffect {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}

/* UI Element Animations */
@keyframes buttonPress {
    0% { transform: scale(1); }
    50% { transform: scale(0.95); }
    100% { transform: scale(1); }
}

@keyframes notification {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }
    10% {
        transform: translateX(0);
        opacity: 1;
    }
    90% {
        transform: translateX(0);
        opacity: 1;
    }
    100% {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes progressBar {
    from { width: 0%; }
    to { width: 100%; }
}

/* Character Animations */
@keyframes characterIdle {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

@keyframes characterAttack {
    0% { transform: translateX(0); }
    25% { transform: translateX(-10px); }
    50% { transform: translateX(5px); }
    100% { transform: translateX(0); }
}

@keyframes characterDamaged {
    0% {
        transform: translateX(0);
        filter: brightness(1);
    }
    25% {
        transform: translateX(-10px);
        filter: brightness(2);
    }
    50% {
        transform: translateX(10px);
        filter: brightness(2);
    }
    75% {
        transform: translateX(-5px);
        filter: brightness(2);
    }
    100% {
        transform: translateX(0);
        filter: brightness(1);
    }
}

/* Level Up Animation */
@keyframes levelUp {
    0% {
        transform: scale(0) rotate(0deg);
        opacity: 0;
    }
    50% {
        transform: scale(1.2) rotate(180deg);
        opacity: 1;
    }
    100% {
        transform: scale(1) rotate(360deg);
        opacity: 0;
    }
}

/* Achievement Animation */
@keyframes achievementUnlock {
    0% {
        transform: translate(-50%, -100%);
        opacity: 0;
    }
    15% {
        transform: translate(-50%, 20px);
        opacity: 1;
    }
    85% {
        transform: translate(-50%, 20px);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -100%);
        opacity: 0;
    }
}

/* Transition Classes */
.fade-enter {
    opacity: 0;
}

.fade-enter-active {
    opacity: 1;
    transition: opacity 300ms ease-in;
}

.fade-exit {
    opacity: 1;
}

.fade-exit-active {
    opacity: 0;
    transition: opacity 300ms ease-out;
}

.slide-enter {
    transform: translateX(100%);
}

.slide-enter-active {
    transform: translateX(0);
    transition: transform 300ms ease-in;
}

.slide-exit {
    transform: translateX(0);
}

.slide-exit-active {
    transform: translateX(-100%);
    transition: transform 300ms ease-out;
}

/* Animation Utility Classes */
.animate-fade-in {
    animation: fadeIn 0.3s ease-in forwards;
}

.animate-fade-out {
    animation: fadeOut 0.3s ease-out forwards;
}

.animate-slide-in {
    animation: slideIn 0.3s ease-in forwards;
}

.animate-slide-out {
    animation: slideOut 0.3s ease-out forwards;
}

.animate-scale-in {
    animation: scaleIn 0.3s ease-in forwards;
}

.animate-scale-out {
    animation: scaleOut 0.3s ease-out forwards;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-bounce {
    animation: bounce 2s infinite;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

/* Animation Modifiers */
.animation-delay-100 { animation-delay: 100ms; }
.animation-delay-200 { animation-delay: 200ms; }
.animation-delay-300 { animation-delay: 300ms; }
.animation-delay-400 { animation-delay: 400ms; }
.animation-delay-500 { animation-delay: 500ms; }

.animation-duration-100 { animation-duration: 100ms; }
.animation-duration-200 { animation-duration: 200ms; }
.animation-duration-300 { animation-duration: 300ms; }
.animation-duration-400 { animation-duration: 400ms; }
.animation-duration-500 { animation-duration: 500ms; } 
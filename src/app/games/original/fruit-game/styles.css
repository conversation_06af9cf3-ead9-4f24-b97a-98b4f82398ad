:root {
  --primary-color: #4A90E2;
  --crystal-color: #7B68EE;
  --gem-color: #9B59B6;
  --success-color: #2ECC71;
  --warning-color: #F1C40F;
  --error-color: #E74C3C;
  --text-color: #ECF0F1;
  --background-dark: #2C3E50;
  --background-light: #34495E;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
  margin: 0;
  min-height: 100vh;
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  color: white;
  font-family: system-ui, -apple-system, sans-serif;
}

.game-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.crystal-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 20%, rgba(123, 104, 238, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(155, 89, 182, 0.2) 0%, transparent 50%);
  filter: blur(20px);
}

.aurora {
  position: absolute;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, 
    rgba(74, 144, 226, 0) 0%,
    rgba(74, 144, 226, 0.1) 50%,
    rgba(74, 144, 226, 0) 100%);
  animation: aurora 15s ease infinite;
}

@keyframes aurora {
  0% { transform: translateX(-100%) rotate(45deg); }
  100% { transform: translateX(100%) rotate(45deg); }
}

.game-wrapper {
  max-width: 1800px;
  margin: 0 auto;
  padding: 1rem;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  transition: background-image 0.3s ease;
}

.header {
  padding: 10px;
}

.title {
  font-size: 2.5rem;
  font-weight: bold;
  color: var(--crystal-color);
  text-shadow: 0 0 10px rgba(155, 89, 182, 0.5);
  margin-bottom: 1rem;
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from { text-shadow: 0 0 10px rgba(155, 89, 182, 0.5); }
  to { text-shadow: 0 0 20px rgba(155, 89, 182, 1); }
}

.game-modes {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.mode-btn {
  background: rgba(74, 144, 226, 0.2);
  border: 2px solid rgba(74, 144, 226, 0.5);
  color: var(--text-color);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mode-btn:hover {
  background: rgba(74, 144, 226, 0.4);
  transform: translateY(-2px);
}

.mode-btn.active {
  background: var(--primary-color);
  border-color: var(--crystal-color);
}

.controls-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem 1.5rem;
  background: rgba(30, 41, 59, 0.85);
  backdrop-filter: blur(8px);
  border-radius: 15px;
  margin-bottom: 0.5rem;
  border: 1px solid rgba(155, 89, 182, 0.3);
}

.left-controls {
  display: flex;
  gap: 1rem;
}

.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-content {
  display: none;
  position: absolute;
  background: var(--background-light);
  min-width: 160px;
  box-shadow: 0 8px 16px rgba(0,0,0,0.2);
  z-index: 1;
  border-radius: 10px;
  overflow: hidden;
}

.dropdown:hover .dropdown-content {
  display: block;
}

.dropdown-content .mode-btn {
  display: block;
  width: 100%;
  text-align: left;
  border-radius: 0;
  border: none;
  padding: 0.8rem 1rem;
}

.dropdown-content .mode-btn:hover {
  background: rgba(74, 144, 226, 0.4);
  transform: none;
}

.stats-bar {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.2rem;
}

.game-container {
  display: flex;
  justify-content: space-between;
  gap: 3rem;
  padding: 1rem;
  height: calc(100vh - 100px);
  max-width: 100%;
}

.game-board, .answers {
  flex: 1;
  display: grid;
  gap: 1.5rem;
  background: rgba(30, 41, 59, 0.85);
  border-radius: 15px;
  padding: 2rem;
  backdrop-filter: blur(8px);
  border: 2px solid rgba(155, 89, 182, 0.5);
  box-shadow: 0 0 20px rgba(155, 89, 182, 0.3);
  width: 48%;
  min-width: 0;
  grid-template-columns: repeat(2, 1fr);
  align-content: start;
  overflow: auto;
}

.cell, .draggable {
  min-height: 80px;
  height: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 1rem;
  font-size: 1.2rem;
  transition: all 0.3s ease;
  position: relative;
  word-break: break-word;
  hyphens: auto;
}

.draggable:hover {
  background: rgba(255, 255, 255, 0.2);
}

.dragging {
  opacity: 0.5;
}

.button {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.gem-icon {
  font-size: 1.2rem;
  margin-left: 0.5rem;
}

.gem-sparkle {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  background: radial-gradient(
    circle at var(--x, 50%) var(--y, 50%),
    rgba(255, 255, 255, 0.2) 0%,
    transparent 50%
  );
  opacity: 0;
  transition: opacity 0.3s;
}

.cell:hover .gem-sparkle,
.draggable:hover .gem-sparkle {
  opacity: 1;
}

.hint-container {
  text-align: center;
  margin-top: 1rem;
}

.hint-btn {
  background: var(--warning-color);
  color: var(--background-dark);
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.hint-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(241, 196, 15, 0.3);
}

.streak-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(46, 204, 113, 0.9);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  display: none;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

@keyframes shimmer {
  0% { transform: translateX(-100%) rotate(45deg); }
  100% { transform: translateX(100%) rotate(45deg); }
}

/* Modal Styles */
.modal-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 100;
}

.modal {
  display: none;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 101;
}

.modal-content {
  background: rgba(30, 41, 59, 0.95);
  padding: 2rem;
  border-radius: 15px;
  width: 95%;
  max-width: 800px;
  backdrop-filter: blur(10px);
  border: 2px solid var(--crystal-color);
  box-shadow: 0 0 30px rgba(155, 89, 182, 0.3);
  color: var(--text-color);
}

.modal h2 {
  font-size: 1.8rem;
  margin-bottom: 1.5rem;
  color: var(--crystal-color);
  text-align: center;
}

.input-group {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  flex-wrap: nowrap;
}

.input-group input {
  flex: 1;
  min-width: 0;
  padding: 0.8rem;
  border-radius: 8px;
  border: 2px solid rgba(155, 89, 182, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-color);
  font-size: 1.1rem;
}

.word-list {
  max-height: 300px;
  overflow-y: auto;
  margin: 1.5rem 0;
  padding: 0.5rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
}

.word-pair {
  padding: 8px;
  margin: 5px 0;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.modal-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
}

.modal-title {
  text-align: center;
  margin-bottom: 1rem;
}

.word-input-group {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.word-input-group input {
  flex: 1;
  padding: 0.5rem;
  border-radius: 5px;
  border: 1px solid var(--crystal-color);
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-color);
}

.btn {
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: 8px;
  background: var(--crystal-color);
  color: white;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn:hover {
  transform: scale(1.05);
  background: var(--primary-color);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.teacher-section {
  margin: 1rem 0;
}

.teacher-section textarea {
  width: 100%;
  height: 150px;
  padding: 0.5rem;
  border-radius: 5px;
  border: 1px solid var(--crystal-color);
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-color);
  margin-bottom: 1rem;
}

.custom-word-list {
  max-height: 200px;
  overflow-y: auto;
  margin-top: 1rem;
  padding: 0.5rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
}

.word-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: rgba(74, 144, 226, 0.2);
  border-radius: 5px;
  margin-bottom: 0.5rem;
}

.word-item button {
  background: var(--error-color);
  color: var(--text-color);
  border: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.win-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.5s ease-out;
}

.win-text {
  background: var(--background-light);
  padding: 3rem;
  border-radius: 25px;
  text-align: center;
  animation: scaleIn 0.5s ease-out;
  border: 4px solid var(--crystal-color);
  box-shadow: 0 0 30px rgba(155, 89, 182, 0.5);
  max-width: 600px;
  width: 90%;
}

.win-text h2 {
  font-size: 3.5rem;
  margin-bottom: 1.5rem;
  color: var(--crystal-color);
  text-shadow: 0 0 20px rgba(155, 89, 182, 0.8);
}

.win-text p {
  font-size: 1.8rem;
  margin-bottom: 2rem;
  color: var(--text-color);
}

.win-text button {
  font-size: 1.4rem;
  padding: 1rem 2rem;
  background: var(--crystal-color);
  border: none;
  border-radius: 25px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.win-text button:hover {
  transform: scale(1.1);
  box-shadow: 0 0 20px rgba(155, 89, 182, 0.8);
}

.share-section,
#shareBtn,
.share-link-container {
  display: none;
}

.sparkle {
  position: absolute;
  background: white;
  border-radius: 50%;
  pointer-events: none;
  animation: sparkleOut 1s ease-out forwards;
}

.drag-trail {
  position: fixed;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: var(--crystal-color);
  pointer-events: none;
  z-index: 1000;
  opacity: 0.5;
  transition: all 0.1s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.8); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes sparkleOut {
  0% {
    transform: scale(0) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: scale(1) rotate(180deg);
    opacity: 0;
  }
}

.cell.correct {
  background: rgba(46, 204, 113, 0.3);
  border-color: var(--success-color);
  animation: correctGlow 2s ease-in-out infinite alternate;
}

@keyframes correctGlow {
  from { box-shadow: 0 0 10px rgba(46, 204, 113, 0.5); }
  to { box-shadow: 0 0 20px rgba(46, 204, 113, 1); }
}

.cell.incorrect {
  animation: incorrectMatch 0.5s ease-out;
  border-color: var(--error-color);
  background: rgba(231, 76, 60, 0.2);
}

@keyframes incorrectMatch {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-10px); }
  75% { transform: translateX(10px); }
}

.matched-pair {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
  height: 100%;
  padding: 0.5rem 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  transition: all 0.3s ease;
}

/* Only show success styling when explicitly marked correct */
.cell.correct .matched-pair {
  background: rgba(46, 204, 113, 0.15);
  box-shadow: inset 0 0 15px rgba(46, 204, 113, 0.2);
  animation: matchedGlow 2s ease-in-out infinite alternate;
}

.cell.incorrect {
  animation: incorrectShake 0.5s ease-in-out;
}

@keyframes incorrectShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-10px); }
  75% { transform: translateX(10px); }
}

.match-arrow {
  font-size: 1rem;
  opacity: 0.6;
  margin: 0 0.5rem;
}

.spanish-word, .english-word {
  word-break: break-word;
  hyphens: auto;
  max-width: calc(50% - 1rem);
  text-align: center;
}

.hint-highlight {
  animation: hintPulse 1s ease-in-out infinite;
  border-color: var(--warning-color) !important;
  box-shadow: 0 0 15px var(--warning-color) !important;
}

@keyframes hintPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Fullscreen styles */
.fullscreen {
  max-width: none !important;
  width: 100vw !important;
  height: 100vh !important;
  padding: 2rem;
  margin: 0;
}

.fullscreen .game-container {
  height: calc(100vh - 120px);
  max-width: none;
}

.fullscreen .game-board,
.fullscreen .answers {
  max-width: none;
  width: 48%;
}

/* Responsive adjustments */
@media (max-width: 1600px) {
  .game-container {
    gap: 2rem;
  }

  .game-board, .answers {
    padding: 1.5rem;
    gap: 1rem;
  }

  .cell, .draggable {
    padding: 0.8rem;
    font-size: 1.1rem;
  }
}

@media (max-width: 1200px) {
  .game-container {
    gap: 1.5rem;
  }

  .game-board, .answers {
    padding: 1rem;
    gap: 0.8rem;
  }

  .cell, .draggable {
    padding: 0.6rem;
    font-size: 1rem;
  }
}

/* Add styles for used draggables */
.draggable.used {
  opacity: 0.5;
  background: rgba(155, 89, 182, 0.2);
  border: 2px solid rgba(155, 89, 182, 0.3);
  cursor: not-allowed;
  pointer-events: none;
  text-decoration: line-through;
  color: rgba(255, 255, 255, 0.6);
  transform: scale(0.95);
  transition: all 0.3s ease;
}

.word-input-section {
  margin-bottom: 2rem;
}

.import-section {
  margin-bottom: 2rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.start-game-btn {
  width: 100%;
  padding: 1rem;
  font-size: 1.2rem;
  background: var(--crystal-color);
  margin-top: 1rem;
}

.start-game-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.time-select-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.time-option {
  padding: 1rem;
  border: none;
  border-radius: 8px;
  background: var(--crystal-color);
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.time-option:hover {
  transform: scale(1.02);
  background: var(--primary-color);
}

.custom-time {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
}

.custom-time input {
  flex: 1;
  padding: 0.8rem;
  border-radius: 8px;
  border: 2px solid rgba(155, 89, 182, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-color);
  font-size: 1.1rem;
}

/* Active mode button */
.mode-btn.active {
  background: var(--crystal-color);
  transform: scale(1.05);
}

.victory-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.95);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.victory-content {
  background: linear-gradient(135deg, var(--background-dark), var(--background-light));
  padding: 3rem;
  border-radius: 20px;
  text-align: center;
  border: 4px solid var(--crystal-color);
  box-shadow: 0 0 50px rgba(155, 89, 182, 0.5);
  width: 90%;
  max-width: 600px;
  animation: scaleIn 0.5s ease-out;
}

.victory-title {
  font-size: 3.5rem;
  color: var(--crystal-color);
  margin-bottom: 1.5rem;
  text-shadow: 0 0 20px rgba(155, 89, 182, 0.8);
}

.victory-message {
  font-size: 1.8rem;
  color: var(--text-color);
  margin-bottom: 2rem;
}

.victory-stats {
  font-size: 1.5rem;
  margin: 2rem 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  color: var(--text-color);
}

.victory-button {
  font-size: 1.4rem;
  padding: 1rem 2rem;
  background: var(--crystal-color);
  color: white;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 1rem;
}

.victory-button:hover {
  transform: scale(1.1);
  box-shadow: 0 0 30px rgba(155, 89, 182, 0.8);
}

@keyframes scaleIn {
  from { transform: scale(0.8); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* Import modal styles */
.import-modal {
  display: none;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 102;
}

.import-textarea {
  width: 100%;
  min-height: 200px;
  margin: 1rem 0;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(155, 89, 182, 0.3);
  border-radius: 8px;
  color: var(--text-color);
  font-size: 1.1rem;
  resize: vertical;
}

.import-instructions {
  color: var(--text-color);
  opacity: 0.8;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  line-height: 1.4;
}

.feedback-message {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--success-color);
  color: white;
  padding: 1rem 2rem;
  border-radius: 25px;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.feedback-message.fade-out {
  animation: fadeOut 0.5s ease-out forwards;
}

@keyframes fadeOut {
  to { opacity: 0; transform: translate(-50%, -20px); }
}

.game-over-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.95);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.game-over-content {
  background: linear-gradient(135deg, var(--background-dark), var(--background-light));
  padding: 3rem;
  border-radius: 20px;
  text-align: center;
  border: 4px solid var(--error-color);
  box-shadow: 0 0 50px rgba(231, 76, 60, 0.5);
  width: 90%;
  max-width: 600px;
  animation: scaleIn 0.5s ease-out;
}

.game-over-title {
  font-size: 3.5rem;
  color: var(--error-color);
  margin-bottom: 1.5rem;
  text-shadow: 0 0 20px rgba(231, 76, 60, 0.8);
}

.game-over-message {
  font-size: 1.8rem;
  color: var(--text-color);
  margin-bottom: 2rem;
}

.game-over-stats {
  font-size: 1.5rem;
  margin: 2rem 0;
  color: var(--text-color);
}

.game-over-button {
  font-size: 1.4rem;
  padding: 1rem 2rem;
  background: var(--error-color);
  color: white;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 1rem;
}

.game-over-button:hover {
  transform: scale(1.1);
  box-shadow: 0 0 30px rgba(231, 76, 60, 0.8);
}

/* Add touch styles */
.touch-device .draggable {
  touch-action: none;
  user-select: none;
  -webkit-user-select: none;
}

.touch-device .draggable.dragging {
  opacity: 0.8;
  transform: scale(1.05);
  box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.touch-device .cell.dragover {
  transform: scale(1.05);
  border-color: var(--crystal-color);
  box-shadow: 0 0 15px var(--crystal-color);
}

/* Theme Styles */
.theme-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
    margin: 20px 0;
}

.theme-option {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    aspect-ratio: 16/9;
    border: 3px solid transparent;
    background: rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.theme-option:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.theme-option img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.theme-option.selected {
    border-color: var(--crystal-color);
    box-shadow: 0 0 20px var(--crystal-color);
}

.theme-option .theme-name {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 12px;
    text-align: center;
    font-size: 1.1rem;
    backdrop-filter: blur(5px);
}

.theme-upload {
    text-align: center;
    margin-top: 20px;
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

#themeModal .modal-content {
    max-width: 900px;
    width: 90%;
    background: rgba(30, 41, 59, 0.95);
    border: 2px solid var(--crystal-color);
}

#themeModal h2 {
    font-size: 2.5rem;
    color: var(--crystal-color);
    text-align: center;
    margin-bottom: 30px;
    text-shadow: 0 0 10px rgba(155, 89, 182, 0.5);
}

#uploadThemeBtn {
    background: var(--crystal-color);
    color: white;
    padding: 15px 30px;
    border-radius: 10px;
    font-size: 1.2rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

#uploadThemeBtn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(155, 89, 182, 0.4);
    background: var(--primary-color);
}

#closeThemeBtn {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    padding: 12px 25px;
    border-radius: 8px;
    font-size: 1.1rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 20px;
}

#closeThemeBtn:hover {
    background: rgba(255, 255, 255, 0.2);
}
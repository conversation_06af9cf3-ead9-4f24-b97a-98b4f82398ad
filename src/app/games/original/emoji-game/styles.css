* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  background: linear-gradient(135deg, #1a2a6c, #b21f1f, #fdbb2d);
  color: #333;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  animation: gradientBG 15s ease infinite;
  background-size: 400% 400%;
  transition: background 0.5s ease;
}

@keyframes gradientBG {
  0% { background-position: 0% 50% }
  50% { background-position: 100% 50% }
  100% { background-position: 0% 50% }
}

.game-wrapper {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  width: 98%;
  height: 95vh;
  max-width: none;
  backdrop-filter: blur(10px);
  transform: translateY(0);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  background-size: cover;
  background-position: center;
}

.game-wrapper:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

.header {
  text-align: center;
  margin-bottom: 15px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  padding: 10px 0;
}

.left-controls,
.right-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.stats-group {
  display: flex;
  gap: 15px;
  margin-right: 10px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.1rem;
  color: #1a2a6c;
  text-shadow: 
    0 0 8px rgba(255, 255, 255, 0.8),
    1px 1px 2px rgba(255, 255, 255, 0.9);
  font-weight: 600;
}

.stat-item i {
  color: #4a90e2;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.8);
}

.title {
  font-size: 2.2rem;
  color: #1a2a6c;
  text-shadow: 
    0 0 10px rgba(255, 255, 255, 0.8),
    2px 2px 4px rgba(255, 255, 255, 0.9);
  margin: 0;
  text-align: center;
  white-space: nowrap;
  font-weight: 700;
}

.nav-btn {
  padding: 8px 15px;
  border: none;
  border-radius: 8px;
  background: linear-gradient(135deg, #4a90e2, #7e57c2);
  color: white;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 5px;
  white-space: nowrap;
}

.nav-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

#gameBoard {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 25px;
  width: 100%;
  height: 100%;
  padding: 25px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 15px;
  backdrop-filter: blur(10px);
}

.cell {
  aspect-ratio: 1;
  min-width: 150px;
  min-height: 150px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  position: relative;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  padding: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: default;
}

.emoji-container {
  position: absolute;
  inset: 0;
  margin: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3.5rem;
  transition: transform 0.3s ease;
  z-index: 1;
}

.emoji-container img {
  max-width: 80%;
  max-height: 80%;
  object-fit: contain;
}

/* Style for when dragging over a cell */
.cell.drag-over {
  background: rgba(74, 144, 226, 0.05);
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.5);
  transform: scale(1.02);
}

/* Full overlay style */
.dropped-answer.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(74, 144, 226, 0.95);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  font-weight: 500;
  border-radius: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* Bottom placement style */
.dropped-answer.bottom {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(74, 144, 226, 0.95);
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* Show the answer when matched */
.cell.matched .dropped-answer {
  opacity: 1;
}

/* Style for draggable words */
.draggable {
  background: linear-gradient(135deg, #4a90e2, #7e57c2);
  color: white;
  padding: 15px 25px;
  border-radius: 8px;
  cursor: move;
  transition: all 0.3s ease;
  font-size: 1.2rem;
  font-weight: 700;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  user-select: none;
  margin-bottom: 12px;
  -webkit-user-drag: element;
}

.draggable:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.draggable.dragging {
  opacity: 0.8;
  transform: scale(1.05);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

/* Correct/Wrong states */
.cell.correct .dropped-answer {
  background: rgba(72, 187, 120, 0.95);
}

.cell.wrong .dropped-answer {
  background: rgba(245, 101, 101, 0.95);
  animation: shake 0.5s ease;
}

@keyframes shake {
  0%, 100% { transform: translateX(-50%); }
  25% { transform: translateX(calc(-50% - 5px)); }
  75% { transform: translateX(calc(-50% + 5px)); }
}

.win-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.win-content {
  background: white;
  padding: 40px;
  border-radius: 20px;
  text-align: center;
  animation: scaleIn 0.5s ease;
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.win-text {
  font-size: 2rem;
  color: #1a2a6c;
  margin-bottom: 20px;
}

.win-score {
  font-size: 1.2rem;
  color: #4a90e2;
  margin: 10px 0;
}

.win-time {
  font-size: 1.2rem;
  color: #7e57c2;
  margin-bottom: 20px;
}

.btn {
  padding: 12px 25px;
  border: none;
  border-radius: 10px;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
}

.btn-primary {
  background: linear-gradient(135deg, #1a2a6c, #b21f1f);
  color: white;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Fullscreen styles */
.fullscreen .game-wrapper {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 20px;
  border-radius: 0;
}

.fullscreen #gameBoard {
  height: calc(100vh - 250px);
}

/* Responsive styles */
@media (max-width: 768px) {
  .header-content {
    flex-wrap: wrap;
  }

  .title {
    font-size: 1.8rem;
    order: -1;
    width: 100%;
  }

  .left-controls,
  .right-controls {
    flex: 1;
  }

  .left-controls {
    justify-content: flex-start;
  }

  .right-controls {
    justify-content: flex-end;
  }

  .stats-group {
    gap: 10px;
  }

  .stat-item {
    font-size: 0.9rem;
  }

  #gameBoard {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    padding: 15px;
  }

  .cell {
    min-width: 120px;
    min-height: 120px;
  }
}

/* Theme styles */
.theme-forest .game-wrapper {
  background: url('backgrounds/forest.jpg') center/cover no-repeat;
}

.theme-temple .game-wrapper {
  background: url('backgrounds/temple_of_chaos.jpg') center/cover no-repeat;
}

.theme-classroom .game-wrapper {
  background: url('backgrounds/typical classroom.jpg') center/cover no-repeat;
}

.theme-cave .game-wrapper {
  background: url('backgrounds/cave_of_memories.jpg') center/cover no-repeat;
}

/* Theme selector styles */
.theme-selector {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

.theme-option {
  position: relative;
  aspect-ratio: 1;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 3px solid transparent;
  overflow: hidden;
}

.theme-option:hover {
  transform: scale(1.05);
}

.theme-option.active {
  border-color: #4a90e2;
  box-shadow: 0 0 15px rgba(74, 144, 226, 0.3);
}

.theme-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.theme-name {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px;
  text-align: center;
  font-size: 0.9rem;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.theme-option:hover .theme-name {
  transform: translateY(0);
}

/* Update game wrapper for themes */
.game-wrapper {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  width: 98%;
  height: 95vh;
  max-width: none;
  backdrop-filter: blur(10px);
  transform: translateY(0);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  background-size: cover;
  background-position: center;
}

.game-wrapper:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

/* Update cell styles for better contrast with themes */
.cell {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(5px);
}

#gameBoard {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

#answers {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

/* Add theme transitions */
body {
  transition: background 0.5s ease;
}

/* Update modal styles for better contrast */
.modal {
  display: none;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  padding: 30px;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  z-index: 1001;
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  z-index: 1000;
}

.modal-content {
  max-width: 600px;
  margin: 0 auto;
}

.modal-title {
  font-size: 1.8rem;
  color: #1a2a6c;
  margin-bottom: 20px;
  text-align: center;
}

.modal-buttons {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 20px;
}

/* Add theme-specific accent colors */
.theme-forest .nav-btn,
.theme-forest .draggable {
  background: linear-gradient(135deg, #2d5a27, #4a9c2d);
}

.theme-temple .nav-btn,
.theme-temple .draggable {
  background: linear-gradient(135deg, #8b4513, #d2691e);
}

.theme-classroom .nav-btn,
.theme-classroom .draggable {
  background: linear-gradient(135deg, #1a4b6c, #2d88b9);
}

.theme-cave .nav-btn,
.theme-cave .draggable {
  background: linear-gradient(135deg, #4a4a4a, #7e7e7e);
}

/* Notification styles */
.notification {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 15px 25px;
  border-radius: 10px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.notification.success {
  border-left: 4px solid #4CAF50;
}

.notification.error {
  border-left: 4px solid #f44336;
}

.notification.warning {
  border-left: 4px solid #ff9800;
}

.notification.info {
  border-left: 4px solid #2196F3;
}

/* Custom word modal styles */
.word-input-group {
    margin-bottom: 20px;
}

.word-entry {
    display: flex;
    align-items: center;
    gap: 10px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 5px 10px;
    margin-bottom: 10px;
}

.word-input {
    flex: 1;
    border: none;
    padding: 8px;
    font-size: 1rem;
    outline: none;
}

.image-search-btn {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.image-search-btn:hover {
    background: #f0f0f0;
    color: #1a2a6c;
}

.custom-word-list {
    max-height: 300px;
    overflow-y: auto;
    margin: 20px 0;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
}

.word-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    background: white;
    border-radius: 6px;
    margin-bottom: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.word-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.word-content img {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;
}

.delete-btn {
    color: #dc3545;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1.2rem;
    opacity: 0.7;
    transition: all 0.2s ease;
    padding: 4px 8px;
    border-radius: 4px;
}

.delete-btn:hover {
    opacity: 1;
    background: #fee2e2;
}

.share-section {
    margin: 20px 0;
    text-align: center;
}

.btn-secondary {
    background: #f0f0f0;
    color: #333;
}

/* Image search modal styles */
.search-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

#imageSearchInput {
    flex: 1;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
}

.image-results {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    max-height: 400px;
    overflow-y: auto;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.image-result-container {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    background: white;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: transform 0.2s ease;
}

.image-result-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.image-result {
    width: 100%;
    aspect-ratio: 16/9;
    object-fit: cover;
    cursor: pointer;
    display: block;
}

.image-credit {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 4px 8px;
    font-size: 0.8rem;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.image-result-container:hover .image-credit {
    opacity: 1;
}

.image-credit a {
    color: white;
    text-decoration: none;
}

.image-credit a:hover {
    text-decoration: underline;
}

.loading-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #666;
    font-size: 0.9rem;
    gap: 10px;
}

.loading-spinner::before {
    content: "";
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #1a2a6c;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Pixabay attribution */
.pixabay-attribution {
    text-align: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
    font-size: 0.9rem;
    color: #666;
}

.pixabay-attribution a {
    color: #1a2a6c;
    text-decoration: none;
    font-weight: 500;
}

.pixabay-attribution a:hover {
    text-decoration: underline;
}

/* Update game container layout */
.game-container {
    display: grid;
    grid-template-columns: minmax(180px, 220px) 1fr;
    gap: 20px;
    flex: 1;
    min-height: 0;
    margin: 10px 0;
    height: calc(100vh - 150px);
}

.words-column {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 12px;
    height: 100%;
}

.canvas-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    height: 100%;
    overflow: auto;
}

#gameBoard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    width: 100%;
    height: 100%;
    padding: 20px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.cell:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.emoji-container {
    position: absolute;
    inset: 0;
    margin: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3.5rem;
    transition: transform 0.3s ease;
    z-index: 1;
}

.cell:hover .emoji-container {
    transform: scale(1.1);
}

.answer-placeholder {
    position: absolute;
    inset: 0;
    margin: auto;
    border: 2px dashed rgba(74, 144, 226, 0.3);
    border-radius: 12px;
    transition: all 0.3s ease;
    pointer-events: none;
    z-index: 2;
}

.cell.drag-over .answer-placeholder {
    border-style: solid;
    border-color: rgba(74, 144, 226, 0.6);
    background: rgba(74, 144, 226, 0.1);
}

.dropped-answer {
    position: absolute;
    inset: 0;
    margin: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(74, 144, 226, 0.95);
    color: white;
    padding: 8px 16px;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 500;
    text-align: center;
    opacity: 1;
    transition: all 0.3s ease;
    z-index: 3;
}

.draggable {
    background: linear-gradient(135deg, #4a90e2, #7e57c2);
    color: white;
    padding: 15px 25px;
    border-radius: 8px;
    cursor: move;
    transition: all 0.3s ease;
    font-size: 1.2rem;
    font-weight: 700;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    text-align: center;
    user-select: none;
    margin-bottom: 12px;
    -webkit-user-drag: element;
}

.draggable:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.draggable.dragging {
    opacity: 0.8;
    transform: scale(1.05);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

/* Fullscreen adjustments */
.fullscreen .game-container {
    height: calc(100vh - 100px);
}

.fullscreen .game-wrapper {
    width: 98%;
    height: 98vh;
    margin: 1vh auto;
    padding: 20px;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
    .game-container {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
        height: auto;
    }

    .words-column {
        max-height: 200px;
    }
}

/* Add this to the script to set cell dimensions */
:root {
    --cell-width: 150px;
    --cell-height: 150px;
}

/* Music Control Styles */
.music-control {
    position: relative;
    display: inline-block;
}

.music-dropdown {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    padding: 8px;
    z-index: 1000;
    min-width: 150px;
}

.music-control:hover .music-dropdown {
    display: block;
}

.music-select {
    width: 100%;
    padding: 8px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    font-size: 0.9rem;
    color: #333;
    background: white;
    cursor: pointer;
    outline: none;
    transition: all 0.3s ease;
}

.music-select:hover {
    border-color: #4a90e2;
}

.music-select:focus {
    border-color: #4a90e2;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.music-select option {
    padding: 8px;
    font-size: 0.9rem;
}

.cell-label {
    position: absolute;
    top: 10px;
    left: 10px;
    background-color: #4A5568;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: bold;
    font-size: 1rem;
    z-index: 2;
}

.answer-number {
    color: #4A5568;
    font-weight: bold;
    margin-right: 4px;
}

.draggable {
    font-size: 1.2rem;
    font-weight: 700;
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
}

.dropped-answer {
    font-size: 1.2rem;
    font-weight: 700;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}
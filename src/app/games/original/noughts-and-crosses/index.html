<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#1a2a6c">
    <meta name="description" content="Tres en raya - A fun Spanish language learning tic-tac-toe game">
    <link rel="manifest" href="manifest.json">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <title>Tres en raya - Spanish Learning Game</title>
</head>
<body>
    <!-- Navigation will be injected here -->
    <script type="module">
        import { injectNavigation } from './../components/navigation.js';
        injectNavigation();
    </script>

    <!-- Top Navigation Bar -->
    <nav class="top-nav">
        <div class="nav-left">
            <a href="../index.html" class="btn" aria-label="Back to home">
                <i class="fas fa-arrow-left"></i>
            </a>
            <button id="categoriesBtn" class="btn" aria-label="Open categories">
                <i class="fas fa-th-list"></i> Categories
            </button>
            <button id="customWordsBtn" class="btn" aria-label="Add custom words">
                <i class="fas fa-edit"></i> Custom Words
            </button>
        </div>
        <h1>Tres en raya</h1>
        <div class="nav-right">
            <button id="toggleMusic" class="btn" aria-label="Toggle music">
                <i class="fas fa-music"></i>
            </button>
            <button id="toggleSound" class="btn" aria-label="Toggle sound effects">
                <i class="fas fa-volume-up"></i>
            </button>
            <button id="themeBtn" class="btn" aria-label="Change theme">
                <i class="fas fa-palette"></i>
            </button>
            <button id="fullscreenBtn" class="btn" aria-label="Toggle fullscreen">
                <i class="fas fa-expand"></i>
            </button>
        </div>
    </nav>

    <div class="game-wrapper">
        <!-- Game Stats -->
        <div class="game-stats">
            <div class="stat">
                <span class="stat-value" id="timer">00:00</span>
                <span class="stat-label">Time</span>
            </div>
            <div class="stat">
                <span class="stat-value" id="score">0</span>
                <span class="stat-label">Score</span>
            </div>
            <div class="stat">
                <span class="stat-value" id="hints">3</span>
                <span class="stat-label">Hints</span>
            </div>
            <div class="stat">
                <span class="stat-value" id="countdown">3</span>
                <span class="stat-label">Move Timer</span>
            </div>
        </div>

        <div class="current-player">
            <span class="player-label">Current Player:</span>
            <span id="currentPlayer" class="player-symbol">X</span>
        </div>

        <!-- Difficulty Level Selector -->
        <div class="difficulty-selector">
            <span class="difficulty-label">Difficulty:</span>
            <div class="difficulty-buttons">
                <button id="easyMode" class="btn difficulty-btn active" data-difficulty="beginner">Easy</button>
                <button id="mediumMode" class="btn difficulty-btn" data-difficulty="intermediate">Medium</button>
                <button id="hardMode" class="btn difficulty-btn" data-difficulty="advanced">Hard</button>
            </div>
        </div>

        <!-- Main Game Area -->
        <div class="game-area">
            <div class="grid" id="grid"></div>
            <div class="answers" id="answers"></div>
        </div>

        <div class="game-controls">
            <button id="resetGame" class="btn" aria-label="Reset game">
                <i class="fas fa-redo"></i> Reset
            </button>
            <button id="showHint" class="btn" aria-label="Get a hint">
                <i class="fas fa-lightbulb"></i> Hint
            </button>
            <button id="shareGame" class="btn" aria-label="Share game">
                <i class="fas fa-share-alt"></i> Share
            </button>
        </div>

        <!-- Game Progress Bar -->
        <div class="progress-container">
            <div class="progress-bar" id="gameProgress"></div>
        </div>
    </div>

    <!-- Categories Modal -->
    <div class="modal" id="categoriesModal">
        <div class="modal-content">
            <h2><i class="fas fa-th-list"></i> Categories</h2>
            <div class="category-grid">
                <div class="category-item" data-category="animals">
                    <i class="fas fa-paw"></i>
                    <span>Animals</span>
                    <div class="category-preview">
                        dog → perro<br>
                        cat → gato<br>
                        bird → pájaro
                    </div>
                </div>
                <div class="category-item" data-category="food">
                    <i class="fas fa-utensils"></i>
                    <span>Food</span>
                    <div class="category-preview">
                        apple → manzana<br>
                        bread → pan<br>
                        water → agua
                    </div>
                </div>
                <div class="category-item" data-category="colors">
                    <i class="fas fa-palette"></i>
                    <span>Colors</span>
                    <div class="category-preview">
                        red → rojo<br>
                        blue → azul<br>
                        green → verde
                    </div>
                </div>
                <div class="category-item" data-category="numbers">
                    <i class="fas fa-hashtag"></i>
                    <span>Numbers</span>
                    <div class="category-preview">
                        one → uno<br>
                        two → dos<br>
                        three → tres
                    </div>
                </div>
                <div class="category-item" data-category="travel">
                    <i class="fas fa-plane"></i>
                    <span>Travel</span>
                    <div class="category-preview">
                        airport → aeropuerto<br>
                        hotel → hotel<br>
                        passport → pasaporte
                    </div>
                </div>
                <div class="category-item" data-category="daily">
                    <i class="fas fa-coffee"></i>
                    <span>Daily Life</span>
                    <div class="category-preview">
                        morning → mañana<br>
                        house → casa<br>
                        work → trabajo
                    </div>
                </div>
            </div>
            <button class="btn close-modal">Close</button>
        </div>
    </div>

    <!-- Custom Words Modal -->
    <div class="modal" id="customWordsModal">
        <div class="modal-content">
            <h2><i class="fas fa-edit"></i> Custom Words</h2>
            <div class="custom-words">
                <textarea id="vocabInput" placeholder="Enter word pairs:&#10;english, spanish&#10;one pair per line"></textarea>
                <div class="word-controls">
                    <button id="pasteFromClipboard" class="btn">
                        <i class="fas fa-paste"></i> Paste
                    </button>
                    <button id="clearInput" class="btn">
                        <i class="fas fa-trash"></i> Clear
                    </button>
                    <button id="addWordPair" class="btn">
                        <i class="fas fa-plus"></i> Add
                    </button>
                </div>
                <div id="wordPairsList" class="word-pairs-list"></div>
                <button id="startGame" class="btn" disabled>
                    <i class="fas fa-play"></i> Start Game
                </button>
            </div>
            <button class="btn close-modal">Close</button>
        </div>
    </div>

    <!-- Theme Selector Modal -->
    <div class="modal" id="themeModal">
        <div class="modal-content">
            <h2><i class="fas fa-palette"></i> Choose Theme</h2>
            <div class="theme-grid">
                <button class="btn theme-btn" data-theme="default">
                    <div class="theme-preview default"></div>
                    Default
                </button>
                <button class="btn theme-btn" data-theme="neon">
                    <div class="theme-preview neon"></div>
                    Neon
                </button>
                <button class="btn theme-btn" data-theme="nature">
                    <div class="theme-preview nature"></div>
                    Nature
                </button>
                <button class="btn theme-btn" data-theme="dark">
                    <div class="theme-preview dark"></div>
                    Dark
                </button>
                <button class="btn theme-btn" data-theme="light">
                    <div class="theme-preview light"></div>
                    Light
                </button>
                <button class="btn theme-btn" data-theme="sunset">
                    <div class="theme-preview sunset"></div>
                    Sunset
                </button>
            </div>
            <button class="btn close-modal">Close</button>
        </div>
    </div>

    <!-- Winning Message -->
    <div class="modal" id="winningMessage" style="display: none;">
        <div class="modal-content">
            <div class="confetti-container" id="confetti"></div>
            <h2 class="winning-message-text" id="winningMessageText"></h2>
            <div class="final-stats">
                <div class="stat">
                    <span class="stat-value" id="finalTime">00:00</span>
                    <span class="stat-label">Time</span>
                </div>
                <div class="stat">
                    <span class="stat-value" id="finalScore">0</span>
                    <span class="stat-label">Score</span>
                </div>
                <div class="stat">
                    <span class="stat-value" id="wordsLearned">0</span>
                    <span class="stat-label">Words Learned</span>
                </div>
            </div>
            <div class="learning-progress">
                <h3>Words You've Learned</h3>
                <div id="learnedWordsList" class="learned-words-list"></div>
            </div>
            <div class="modal-buttons">
                <button id="playAgain" class="btn">
                    <i class="fas fa-redo"></i> Play Again
                </button>
                <button id="nextLevel" class="btn">
                    <i class="fas fa-arrow-right"></i> Next Level
                </button>
                <button id="shareScore" class="btn">
                    <i class="fas fa-share-alt"></i> Share Score
                </button>
            </div>
        </div>
    </div>

    <!-- Audio Elements -->
    <audio id="bgMusic" loop>
        <source src="audio/background.mp3" type="audio/mp3">
    </audio>
    <audio id="correctSound" src="audio/correct.mp3"></audio>
    <audio id="wrongSound" src="audio/wrong.mp3"></audio>
    <audio id="winSound" src="audio/win.mp3"></audio>
    <audio id="clickSound" src="audio/click.mp3"></audio>
    <audio id="countdownSound" src="audio/click.mp3"></audio>

    <!-- Loading Overlay -->
    <div id="loading-overlay">
        <div class="loader"></div>
        <p>Loading game...</p>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.6.0/dist/confetti.browser.min.js"></script>
    <script src="script.js"></script>

</body>
</html>
/* Base Styles */
:root {
    /* Default Theme */
    --primary-color: #1a2a6c;
    --secondary-color: #00c6ff;
    --accent-color: #3cba92;
    --text-color: #ffffff;
    --bg-color: #051e3e;
    --card-bg: rgba(255, 255, 255, 0.1);
    --shadow-color: rgba(0, 198, 255, 0.3);
    --success-color: #4CAF50;
    --error-color: #f44336;
    --warning-color: #ff9800;
    --info-color: #2196F3;
    --neon-glow: 0 0 10px rgba(0, 198, 255, 0.5),
                 0 0 20px rgba(0, 198, 255, 0.3),
                 0 0 30px rgba(0, 198, 255, 0.1);
}

/* Theme Variations */
.theme-neon {
    --primary-color: #ff00ff;
    --secondary-color: #00ffff;
    --accent-color: #ffff00;
    --text-color: #ffffff;
    --bg-color: #000000;
    --card-bg: rgba(255, 0, 255, 0.1);
    --shadow-color: rgba(255, 0, 255, 0.3);
    --neon-glow: 0 0 10px rgba(255, 0, 255, 0.5),
                 0 0 20px rgba(255, 0, 255, 0.3),
                 0 0 30px rgba(255, 0, 255, 0.1);
}

.theme-nature {
    --primary-color: #2ecc71;
    --secondary-color: #27ae60;
    --accent-color: #f1c40f;
    --text-color: #ffffff;
    --bg-color: #145a32;
    --card-bg: rgba(46, 204, 113, 0.1);
    --shadow-color: rgba(46, 204, 113, 0.3);
    --neon-glow: 0 0 10px rgba(46, 204, 113, 0.5),
                 0 0 20px rgba(46, 204, 113, 0.3),
                 0 0 30px rgba(46, 204, 113, 0.1);
}

.theme-dark {
    --primary-color: #333333;
    --secondary-color: #666666;
    --accent-color: #d9534f;
    --text-color: #f8f9fa;
    --bg-color: #121212;
    --card-bg: rgba(30, 30, 30, 0.7);
    --shadow-color: rgba(0, 0, 0, 0.5);
    --neon-glow: 0 0 10px rgba(217, 83, 79, 0.5),
                 0 0 20px rgba(217, 83, 79, 0.3),
                 0 0 30px rgba(217, 83, 79, 0.1);
}

.theme-light {
    --primary-color: #4285f4;
    --secondary-color: #34a853;
    --accent-color: #fbbc05;
    --text-color: #202124;
    --bg-color: #f8f9fa;
    --card-bg: rgba(255, 255, 255, 0.8);
    --shadow-color: rgba(0, 0, 0, 0.1);
    --neon-glow: 0 0 10px rgba(66, 133, 244, 0.3),
                 0 0 20px rgba(66, 133, 244, 0.2),
                 0 0 30px rgba(66, 133, 244, 0.1);
}

.theme-sunset {
    --primary-color: #ff7e5f;
    --secondary-color: #feb47b;
    --accent-color: #ffcf47;
    --text-color: #ffffff;
    --bg-color: #2c1e4d;
    --card-bg: rgba(255, 126, 95, 0.1);
    --shadow-color: rgba(255, 126, 95, 0.3);
    --neon-glow: 0 0 10px rgba(255, 126, 95, 0.5),
                 0 0 20px rgba(255, 126, 95, 0.3),
                 0 0 30px rgba(255, 126, 95, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: var(--bg-color);
    color: var(--text-color);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    position: relative;
    overflow: hidden;
    transition: background-color 0.5s ease;
}

/* Animated Background */
body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(125deg, var(--primary-color), var(--secondary-color), var(--accent-color));
    background-size: 400% 400%;
    animation: gradientBG 15s ease infinite;
    opacity: 0.3;
    z-index: -2;
}

/* Floating Bubbles */
body::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, transparent 0%, var(--bg-color) 100%),
                url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Ccircle cx='50' cy='50' r='3' fill='rgba(255,255,255,0.1)'/%3E%3C/svg%3E");
    background-size: 100px 100px;
    animation: floatingBubbles 60s linear infinite;
    opacity: 0.5;
    z-index: -1;
}

@keyframes gradientBG {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes floatingBubbles {
    from { background-position: 0 0; }
    to { background-position: 100px 100px; }
}

/* Top Navigation */
.top-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: var(--card-bg);
    backdrop-filter: blur(10px);
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 100;
    box-shadow: var(--neon-glow);
}

.nav-left, .nav-right {
    display: flex;
    gap: 10px;
    align-items: center;
}

.top-nav h1 {
    color: var(--text-color);
    font-size: 1.5rem;
    text-shadow: var(--neon-glow);
    margin: 0;
}

.top-nav .btn {
    padding: 8px 15px;
    font-size: 1rem;
}

/* Game Layout */
.game-wrapper {
    margin-top: 70px;
    padding: 20px;
    display: grid;
    grid-template-rows: auto auto auto 1fr auto;
    gap: 20px;
    height: calc(100vh - 90px);
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
}

/* Game Stats */
.game-stats {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 20px;
    padding: 15px;
    background: var(--card-bg);
    border-radius: 15px;
    box-shadow: var(--neon-glow);
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 5px;
}

.stat-value {
    font-size: 1.4rem;
    font-weight: bold;
    color: var(--accent-color);
    text-shadow: var(--neon-glow);
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Current Player Display */
.current-player {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    padding: 15px;
    background: var(--card-bg);
    border-radius: 15px;
    box-shadow: var(--neon-glow);
}

.player-label {
    font-size: 1.2rem;
}

.player-symbol {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--accent-color);
    text-shadow: var(--neon-glow);
    transition: all 0.3s ease;
}

/* Difficulty Selector */
.difficulty-selector {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    padding: 10px;
    background: var(--card-bg);
    border-radius: 15px;
    box-shadow: var(--neon-glow);
}

.difficulty-label {
    font-size: 1.1rem;
}

.difficulty-buttons {
    display: flex;
    gap: 10px;
}

.difficulty-btn {
    padding: 8px 15px;
    font-size: 0.9rem;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.difficulty-btn.active {
    opacity: 1;
    background-color: var(--accent-color);
    box-shadow: 0 0 10px var(--accent-color);
}

/* Game Area */
.game-area {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 20px;
    height: 100%;
    overflow: hidden;
}

/* Grid */
.grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 15px;
    background: var(--card-bg);
    padding: 20px;
    border-radius: 15px;
    box-shadow: var(--neon-glow);
}

.cell {
    aspect-ratio: 1;
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid var(--primary-color);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.cell:hover {
    transform: scale(1.05);
    box-shadow: 0 0 20px var(--primary-color);
}

.cell.drag-over {
    border-color: var(--accent-color);
    box-shadow: 0 0 20px var(--accent-color);
}

.cell.correct {
    border-color: var(--success-color);
    background: rgba(76, 175, 80, 0.2);
    animation: pulse 1s ease infinite;
}

.cell.incorrect {
    border-color: var(--error-color);
    background: rgba(244, 67, 54, 0.2);
    animation: shake 0.5s ease;
}

/* Match Animation */
.match-animation {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 5px;
    width: 100%;
    height: 100%;
}

.match-animation .symbol {
    font-size: 2rem;
    font-weight: bold;
    color: var(--accent-color);
    text-shadow: var(--neon-glow);
}

.match-animation .word {
    font-size: 0.9rem;
    color: var(--text-color);
}

/* Answers List */
.answers {
    display: flex;
    flex-direction: column;
    gap: 10px;
    background: var(--card-bg);
    padding: 20px;
    border-radius: 15px;
    box-shadow: var(--neon-glow);
    overflow-y: auto;
}

.draggable {
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    cursor: move;
    transition: all 0.3s ease;
    user-select: none;
    font-size: 1.1rem;
    text-align: center;
}

.draggable:hover {
    transform: translateX(10px);
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 0 15px var(--primary-color);
}

.draggable.dragging {
    opacity: 0.7;
    transform: scale(1.05);
}

/* Game Controls */
.game-controls {
    display: flex;
    justify-content: center;
    gap: 20px;
    padding: 10px;
}

.game-controls .btn {
    padding: 12px 25px;
    font-size: 1.1rem;
}

/* Progress Bar */
.progress-container {
    width: 100%;
    height: 10px;
    background: var(--card-bg);
    border-radius: 5px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    width: 0%;
    transition: width 0.3s ease;
}

/* Button Styles */
.btn {
    background: var(--primary-color);
    color: var(--text-color);
    border: none;
    border-radius: 10px;
    padding: 10px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Poppins', sans-serif;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
}

.btn:hover {
    background: var(--secondary-color);
    box-shadow: 0 0 15px var(--secondary-color);
    transform: translateY(-2px);
}

.btn:active {
    transform: translateY(0);
}

.btn i {
    font-size: 1.1em;
}

.btn-icon {
    padding: 8px;
    border-radius: 50%;
    aspect-ratio: 1;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal.show {
    opacity: 1;
}

.modal-content {
    background: var(--bg-color);
    border-radius: 15px;
    padding: 30px;
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    box-shadow: var(--neon-glow);
    border: 1px solid var(--primary-color);
    animation: modalIn 0.5s ease;
}

@keyframes modalIn {
    from {
        transform: scale(0.8);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

.modal h2 {
    color: var(--accent-color);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.modal h2 i {
    font-size: 1.5rem;
}

/* Category Grid */
.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.category-item {
    background: var(--card-bg);
    border-radius: 15px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.category-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1), 0 0 20px var(--primary-color);
}

.category-item i {
    font-size: 2rem;
    color: var(--accent-color);
}

.category-item span {
    font-size: 1.2rem;
    font-weight: 600;
}

.category-preview {
    font-size: 0.9rem;
    opacity: 0.7;
    text-align: center;
    line-height: 1.5;
}

/* Loading Indicator */
.category-item.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1;
}

.category-item.loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    margin: -20px 0 0 -20px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--accent-color);
    animation: spin 1s ease-in-out infinite;
    z-index: 2;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Custom Words Form */
.custom-words {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

#vocabInput {
    background: var(--card-bg);
    border: 1px solid var(--primary-color);
    color: var(--text-color);
    border-radius: 10px;
    padding: 15px;
    font-family: inherit;
    height: 150px;
    resize: vertical;
}

.word-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.word-pairs-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-height: 200px;
    overflow-y: auto;
    padding: 10px;
    background: var(--card-bg);
    border-radius: 10px;
}

.word-pair {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Theme Selection */
.theme-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 15px;
    margin-bottom: 30px;
}

.theme-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    text-align: center;
    padding: 15px;
}

.theme-preview {
    width: 100%;
    height: 60px;
    border-radius: 10px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.theme-btn.active .theme-preview {
    border-color: var(--accent-color);
    box-shadow: 0 0 15px var(--accent-color);
}

.theme-preview.default {
    background: linear-gradient(45deg, #1a2a6c, #00c6ff, #3cba92);
}

.theme-preview.neon {
    background: linear-gradient(45deg, #ff00ff, #00ffff, #ffff00);
}

.theme-preview.nature {
    background: linear-gradient(45deg, #2ecc71, #27ae60, #f1c40f);
}

.theme-preview.dark {
    background: linear-gradient(45deg, #333333, #666666, #d9534f);
}

.theme-preview.light {
    background: linear-gradient(45deg, #4285f4, #34a853, #fbbc05);
}

.theme-preview.sunset {
    background: linear-gradient(45deg, #ff7e5f, #feb47b, #ffcf47);
}

/* Winning Message */
.winning-message-text {
    font-size: 2rem;
    text-align: center;
    margin: 20px 0;
    color: var(--accent-color);
    text-shadow: var(--neon-glow);
    animation: pulse 2s ease infinite;
}

.final-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin: 30px 0;
}

.modal-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 30px;
}

/* Confetti Container */
.confetti-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

/* Learning Progress */
.learning-progress {
    background: var(--card-bg);
    border-radius: 15px;
    padding: 20px;
    margin-top: 20px;
}

.learning-progress h3 {
    margin-bottom: 15px;
    color: var(--secondary-color);
}

.learned-words-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
    max-height: 150px;
    overflow-y: auto;
}

.learned-word {
    background: rgba(255, 255, 255, 0.05);
    padding: 10px;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.learned-word-english {
    font-weight: bold;
    color: var(--accent-color);
}

/* Notification */
.notification {
    position: fixed;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%) translateY(100px);
    background: var(--card-bg);
    backdrop-filter: blur(10px);
    padding: 15px 25px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: var(--neon-glow);
    z-index: 1000;
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(-50%) translateY(0);
}

.notification i {
    font-size: 1.5rem;
}

.notification-success i {
    color: var(--success-color);
}

.notification-error i {
    color: var(--error-color);
}

.notification-warning i {
    color: var(--warning-color);
}

.notification-info i {
    color: var(--info-color);
}

/* Loading Overlay */
#loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-color);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    transition: opacity 0.5s ease;
}

.loader {
    width: 60px;
    height: 60px;
    border: 5px solid rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    border-top-color: var(--accent-color);
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

/* Animations */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    20%, 60% { transform: translateX(-5px); }
    40%, 80% { transform: translateX(5px); }
}

@keyframes scoreIncrease {
    0% { transform: scale(1); color: var(--success-color); }
    50% { transform: scale(1.3); color: var(--success-color); }
    100% { transform: scale(1); color: var(--accent-color); }
}

@keyframes scoreDecrease {
    0% { transform: scale(1); color: var(--error-color); }
    50% { transform: scale(1.3); color: var(--error-color); }
    100% { transform: scale(1); color: var(--accent-color); }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .game-area {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr auto;
    }
    
    .answers {
        min-height: 150px;
    }
}

@media (max-width: 768px) {
    .game-wrapper {
        margin-top: 60px;
        height: calc(100vh - 80px);
    }
    
    .top-nav {
        padding: 8px 10px;
    }
    
    .top-nav h1 {
        font-size: 1.2rem;
    }
    
    .nav-left .btn, .nav-right .btn {
        padding: 6px 10px;
        font-size: 0.9rem;
    }
    
    .game-stats {
        flex-direction: row;
        flex-wrap: wrap;
        padding: 10px;
    }
    
    .stat {
        flex: 1 1 calc(50% - 10px);
        min-width: 80px;
    }
    
    .stat-value {
        font-size: 1.2rem;
    }
    
    .stat-label {
        font-size: 0.8rem;
    }
    
    .cell {
        font-size: 1rem;
    }
    
    .match-animation .symbol {
        font-size: 1.5rem;
    }
    
    .match-animation .word {
        font-size: 0.8rem;
    }
    
    .game-controls .btn {
        padding: 8px 15px;
        font-size: 0.9rem;
    }
    
    .modal-content {
        padding: 20px;
        width: 95%;
    }
    
    .category-grid, .theme-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
    
    .difficulty-selector {
        flex-direction: column;
        padding: 10px;
    }
    
    .difficulty-buttons {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .top-nav h1 {
        display: none;
    }
    
    .nav-left, .nav-right {
        flex: 1;
    }
    
    .nav-right {
        justify-content: flex-end;
    }
    
    .game-stats {
        gap: 10px;
    }
    
    .stat {
        flex: 1 1 calc(50% - 5px);
        min-width: 70px;
    }
    
    .cell {
        border-radius: 8px;
    }
    
    .grid {
        gap: 8px;
        padding: 10px;
    }
    
    .game-controls {
        gap: 10px;
    }
    
    .game-controls .btn {
        padding: 6px 12px;
        font-size: 0.8rem;
    }
    
    .draggable {
        padding: 10px;
        font-size: 0.9rem;
    }
}
/* Animation for climbing */
@keyframes climb {
  0% {
    transform: translateY(0) scale(1) rotate(0deg);
  }
  50% {
    transform: translateY(-15px) scale(1.05) rotate(-5deg);
  }
  100% {
    transform: translateY(-20px) scale(1) rotate(0deg);
  }
}

/* Animation for falling */
@keyframes fall {
  0% {
    transform: translateY(0) scale(1) rotate(0deg);
  }
  30% {
    transform: translateY(20px) scale(0.95) rotate(8deg);
  }
  100% {
    transform: translateY(0) scale(1) rotate(0deg);
  }
}

/* Ladder rung hover effect */
.ladder-rung:hover {
  filter: brightness(1.2);
  transition: filter 0.3s ease;
}

/* Animated background for space theme - enhanced with more depth */
.space-bg {
  background: linear-gradient(185deg, #0f172a 0%, #1e1b4b 40%, #3b0764 80%, #701a75 100%);
  background-size: 200% 200%;
  position: relative;
  overflow: hidden;
  color: white;
  animation: gradientBG 25s ease infinite;
}

/* Distant stars layer */
.space-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(1px 1px at 10% 20%, #ffffff, rgba(0,0,0,0)),
    radial-gradient(2px 2px at 30% 50%, #aaaaff, rgba(0,0,0,0)),
    radial-gradient(1px 1px at 70% 10%, #ffffff, rgba(0,0,0,0)),
    radial-gradient(1px 1px at 90% 60%, #ffffff, rgba(0,0,0,0)),
    radial-gradient(2px 2px at 50% 80%, #aaaaff, rgba(0,0,0,0));
  background-size: 500px 500px;
  background-repeat: repeat;
  animation: twinkling 15s linear infinite alternate;
  opacity: 0.6;
  z-index: 1;
}

/* Closer, brighter stars layer */
.space-bg::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(2px 2px at 25% 35%, #ffffff, rgba(0,0,0,0)),
    radial-gradient(3px 3px at 55% 65%, #ffffaa, rgba(0,0,0,0)),
    radial-gradient(2px 2px at 85% 25%, #ffffff, rgba(0,0,0,0));
  background-size: 300px 300px;
  background-repeat: repeat;
  animation: twinkling 10s linear infinite;
  opacity: 0.9;
  z-index: 2;
}

/* Space environment elements */
.space-planet {
  position: absolute;
  width: 80px;
  height: 80px;
  opacity: 0.8;
  z-index: 3;
}

.space-planet-1 {
  top: 10%;
  left: 10%;
  animation: planetFloat 20s ease-in-out infinite;
}

.space-planet-2 {
  bottom: 15%;
  right: 8%;
  animation: planetFloat 25s ease-in-out infinite reverse;
}

.space-station {
  position: absolute;
  top: 30%;
  right: 20%;
  z-index: 3;
  animation: stationFloat 30s linear infinite;
  transform-origin: center;
}

.space-wormhole {
  position: absolute;
  width: 120px;
  height: 120px;
  top: 40%;
  left: 15%;
  z-index: 3;
  opacity: 0.7;
  animation: wormholePulse 8s ease-in-out infinite;
}

@keyframes wormholePulse {
  0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.5; }
  50% { transform: scale(1.2) rotate(180deg); opacity: 0.7; }
}

@keyframes stationFloat {
  0% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(3deg); }
  100% { transform: translateY(0) rotate(0deg); }
}

@keyframes planetFloat {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-20px); }
}

@keyframes twinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

@keyframes twinkling {
  0% {
    transform: translate(0, 0);
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translate(10px, -10px);
    opacity: 0.5;
  }
}

@keyframes gradientBG {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Ocean theme - Enhanced with depth and underwater effects */
.ocean-bg {
  background: linear-gradient(180deg, #0ea5e9 0%, #0284c7 40%, #036aa1 70%, #0c4a6e 100%);
  position: relative;
  overflow: hidden;
  color: white;
}

/* Water texture/caustics */
.ocean-bg::before {
  content: '';
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='120' height='120' viewBox='0 0 120 120' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.02' numOctaves='3' stitchTiles='stitch'/%3E%3CfeColorMatrix type='matrix' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
  opacity: 0.12;
  animation: subtleWater 20s linear infinite alternate;
  z-index: 1;
}

/* Light rays effect */
.ocean-bg::after {
  content: '';
  position: absolute;
  top: -50%; left: -50%;
  width: 200%; height: 200%;
  background: linear-gradient(to bottom, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0) 60%);
  transform: rotate(20deg);
  animation: lightRays 30s ease-in-out infinite;
  z-index: 2;
  pointer-events: none;
}

@keyframes subtleWater {
  0% { transform: scale(1) translate(0, 0); }
  100% { transform: scale(1.1) translate(5px, 10px); }
}

@keyframes lightRays {
  0%, 100% { transform: rotate(15deg) translateY(-10px); opacity: 0.4; }
  50% { transform: rotate(25deg) translateY(10px); opacity: 0.6; }
}

/* Ocean environment elements */
.ocean-bubble {
  position: absolute;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
  z-index: 3;
  animation: bubbleFloat linear infinite;
}

.ocean-fish {
  position: absolute;
  width: 30px;
  height: 15px;
  background: #f97316;
  border-radius: 50% 0 0 50%;
  z-index: 3;
  opacity: 0.8;
  animation: fishSwim 20s linear infinite;
}

.ocean-fish-1 {
  top: 30%;
  left: -5%;
  animation-duration: 25s;
}

.ocean-fish-2 {
  top: 50%;
  left: -10%;
  background: #3b82f6;
  animation-duration: 30s;
  transform: scale(0.8);
}

.ocean-fish-3 {
  top: 70%;
  left: -8%;
  background: #a855f7;
  animation-duration: 22s;
  transform: scale(1.2);
}

.ocean-coral {
  position: absolute;
  bottom: 0;
  z-index: 3;
  opacity: 0.7;
}

.ocean-coral-1 {
  left: 10%;
  width: 70px;
  height: 50px;
  background: radial-gradient(circle at 50% 0%, #f472b6 0%, #db2777 100%);
  border-radius: 50% 50% 10% 10% / 80% 80% 30% 30%;
}

.ocean-coral-2 {
  right: 15%;
  width: 60px;
  height: 70px;
  background: radial-gradient(circle at 50% 0%, #fb923c 0%, #ea580c 100%);
  border-radius: 20% 20% 50% 50% / 20% 20% 80% 80%;
}

@keyframes bubbleFloat {
  0% { 
    transform: translateY(0) translateX(0) scale(0.8); 
    opacity: 0.8;
  }
  100% { 
    transform: translateY(-100vh) translateX(20px) scale(1.2); 
    opacity: 0;
  }
}

@keyframes fishSwim {
  0% {
    transform: translateX(-30px) translateY(0);
  }
  20% {
    transform: translateX(calc(100vw + 30px)) translateY(20px);
  }
  21% {
    transform: translateX(calc(100vw + 30px)) translateY(20px) scaleX(-1);
  }
  50% {
    transform: translateX(30px) translateY(0) scaleX(-1);
  }
  51% {
    transform: translateX(30px) translateY(0);
  }
  100% {
    transform: translateX(-30px) translateY(0);
  }
}

/* Mountain theme - Enhanced with dynamic elements */
.mountain-bg {
  background: linear-gradient(180deg, #e0f2fe 0%, #bae6fd 40%, #7dd3fc 70%, #38bdf8 100%);
  position: relative;
  overflow: hidden;
  color: white;
}

/* Mountain range silhouette */
.mountain-bg::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 70%;
  background: linear-gradient(to top, #44403c 0%, #57534e 50%, #78716c 100%);
  clip-path: polygon(0% 100%, 15% 80%, 33% 90%, 45% 70%, 60% 85%, 75% 60%, 90% 75%, 100% 60%, 100% 100%);
  z-index: 1;
}

/* Snow caps */
.mountain-bg::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 70%;
  background: white;
  clip-path: polygon(15% 80%, 20% 78%, 25% 80%, 33% 90%, 40% 87%, 45% 70%, 52% 68%, 60% 85%, 68% 82%, 75% 60%, 80% 58%, 90% 75%, 95% 70%, 100% 60%, 100% 50%, 95% 52%, 90% 60%, 80% 45%, 70% 55%, 60% 65%, 50% 50%, 40% 65%, 30% 70%, 20% 65%, 10% 70%, 0% 60%, 0% 90%, 10% 83%);
  opacity: 0.7;
  z-index: 2;
}

/* Mountain environment elements */
.mountain-cloud {
  position: absolute;
  background: white;
  border-radius: 50%;
  opacity: 0.9;
  z-index: 3;
  animation: cloudFloat linear infinite;
  filter: blur(1px);
}

.mountain-cloud-1 {
  top: 20%;
  left: -10%;
  width: 100px;
  height: 40px;
  animation-duration: 90s;
}

.mountain-cloud-2 {
  top: 15%;
  left: -15%;
  width: 120px;
  height: 50px;
  animation-duration: 120s;
  animation-delay: -30s;
}

.mountain-cloud-3 {
  top: 25%;
  left: -20%;
  width: 80px;
  height: 30px;
  animation-duration: 100s;
  animation-delay: -60s;
}

.mountain-peak {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60%;
  z-index: 1;
}

@keyframes cloudFloat {
  0% {
    transform: translateX(-10%);
  }
  100% {
    transform: translateX(110%);
  }
}

/* Classic theme - Simple but refined */
.classic-bg {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 50%, #93c5fd 100%);
  position: relative;
  overflow: hidden;
}

/* Subtle pattern */
.classic-bg::before {
  content: '';
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%232563eb' fill-opacity='0.08' fill-rule='evenodd'/%3E%3C/svg%3E");
  animation: classicPatternScroll 60s linear infinite;
  opacity: 0.4;
}

@keyframes classicPatternScroll {
  0% { background-position: 0 0; }
  100% { background-position: 160px 160px; }
}

/* Player theme-specific styles - Enhanced Astronaut */
.player-astronaut {
  width: 50px;
  height: 60px;
  background: linear-gradient(to bottom, #e0e0e0, #a0a0a0);
  border: 2px solid #555;
  border-radius: 50% 50% 30% 30% / 60% 60% 40% 40%;
  position: relative;
  box-shadow: 0 6px 10px rgba(0,0,0,0.4), inset 0 2px 3px rgba(255,255,255,0.5);
  transition: transform 0.3s ease-out, box-shadow 0.3s ease;
  animation: playerIdleAstronaut 3s ease-in-out infinite;
}

/* Astronaut helmet visor */
.player-astronaut::before {
  content: '';
  position: absolute;
  top: 10%;
  left: 15%;
  width: 70%;
  height: 40%;
  background: radial-gradient(circle at 30% 30%, #fff, #66aaff 80%, #003399);
  border-radius: 40%;
  border: 1px solid #333;
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.5);
}

/* Astronaut reflection */
.player-astronaut::after {
  content: '';
  position: absolute;
  top: 15%;
  left: 25%;
  width: 15%;
  height: 15%;
  background: white;
  border-radius: 50%;
  opacity: 0.8;
  filter: blur(1px);
}

@keyframes playerIdleAstronaut {
  0%, 100% {
    transform: translateY(0) rotate(-2deg);
  }
  50% {
    transform: translateY(-5px) rotate(2deg);
  }
}

/* Enhanced Player Styles - Diver */
.player-diver {
  width: 50px;
  height: 50px;
  background: radial-gradient(circle at 70% 30%, #ffd700, #facc15 50%, #eab308);
  border-radius: 50%;
  border: 3px solid #a16207;
  position: relative;
  box-shadow: 0 5px 8px rgba(0,0,0,0.3), inset 0 1px 2px rgba(255,255,255,0.4);
  animation: playerIdleDiver 3.5s ease-in-out infinite;
}

/* Diver mask */
.player-diver::before {
  content: '';
  position: absolute;
  top: 20%; left: 50%;
  transform: translateX(-50%);
  width: 60%; height: 35%;
  background: radial-gradient(circle, #e0ffff, #afeeee 70%);
  border: 2px solid #555;
  border-radius: 40% 40% 30% 30%;
}

/* Diver bubble trail */
.player-diver::after {
  content: '';
  position: absolute;
  top: -5px; left: 60%;
  width: 10px; height: 10px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: diverBubble 1.5s ease-out infinite;
  opacity: 0;
}

@keyframes playerIdleDiver {
  0%, 100% { transform: translateY(0) rotate(3deg); }
  50% { transform: translateY(-4px) rotate(-3deg); }
}

@keyframes diverBubble {
  0% { transform: translate(0, 0) scale(0.5); opacity: 0; }
  50% { opacity: 0.8; }
  100% { transform: translate(5px, -25px) scale(1.2); opacity: 0; }
}

/* Enhanced Player Styles - Mountain Climber */
.player-climber {
  width: 45px; height: 55px;
  background: linear-gradient(to bottom, #f87171, #ef4444);
  border: 2px solid #991b1b;
  border-radius: 40% 40% 20% 20%;
  position: relative;
  box-shadow: 0 4px 7px rgba(0,0,0,0.25);
  animation: playerIdleClimber 2.8s ease-in-out infinite;
}

/* Climber helmet */
.player-climber::before {
  content: '';
  position: absolute;
  top: -5%; left: 50%;
  transform: translateX(-50%);
  width: 60%; height: 35%;
  background: linear-gradient(#d1d5db, #9ca3af);
  border-radius: 50% 50% 10% 10% / 80% 80% 20% 20%;
  border: 1px solid #4b5563;
}

/* Climbing gear */
.player-climber::after {
  content: '';
  position: absolute;
  bottom: 10%; right: -8px;
  width: 15px; height: 15px;
  border: 3px solid #ca8a04;
  border-radius: 50%;
  transform: rotate(-30deg);
  box-shadow: -10px 5px 0 -5px #ca8a04;
}

@keyframes playerIdleClimber {
  0%, 100% { transform: translateY(0) scale(1) rotate(-2deg); }
  50% { transform: translateY(-5px) scale(1.02) rotate(2deg); }
}

/* Default Player Style (Classic Theme) */
.player-default {
  width: 45px; height: 45px;
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
  border: 2px solid #1d4ed8;
  border-radius: 50%;
  box-shadow: 0 3px 6px rgba(0,0,0,0.2), inset 0 1px 2px rgba(255,255,255,0.5);
  position: relative;
  animation: playerIdleDefault 4s ease-in-out infinite;
}

/* Inner gear shape */
.player-default::before {
  content: '';
  position: absolute;
  inset: 6px;
  background: white;
  border-radius: 50%;
  clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
  opacity: 0.8;
}

@keyframes playerIdleDefault {
  0%, 100% { transform: rotate(0deg); }
  50% { transform: rotate(10deg); }
}

/* Full-screen mode styles */
.fullscreen-game {
  position: fixed !important;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Optimized game container for better space utilization */
.game-container {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  height: 100%;
}

.game-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

/* Split view for better full-screen layout */
.split-view {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  height: 100%;
}

@media (min-width: 768px) {
  .split-view {
    flex-direction: row;
  }
}

.ladder-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem;
  min-height: 400px;
}

.challenge-container {
  flex: 2;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 2rem;
}

/* Improved ladder visuals */
.ladder {
  position: relative;
  height: 90%;
  width: 120px;
}

.ladder-pole {
  position: absolute;
  width: 8px;
  height: 100%;
  border-radius: 4px;
}

.ladder-pole-left {
  left: 20px;
}

.ladder-pole-right {
  right: 20px;
}

.ladder-rung {
  position: absolute;
  height: 8px;
  width: calc(100% - 20px);
  left: 10px;
  border-radius: 4px;
}

/* Pulsing effect for time indicator when low */
.time-critical {
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

/* Improved button styles */
.game-button {
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  border-radius: 9999px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.game-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.game-button:active {
  transform: translateY(1px);
}

/* Shiny effect for correct answers */
.correct-answer {
  position: relative;
  overflow: hidden;
}

.correct-answer::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 25%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0.1) 75%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  animation: shine 1.5s ease;
}

@keyframes shine {
  0% {
    left: -150%;
    top: -150%;
  }
  100% {
    left: 100%;
    top: 100%;
  }
}

/* Shake effect for incorrect answers */
.incorrect-answer {
  animation: shake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97);
}

@keyframes shake {
  0%, 100% { transform: translateX(0) rotate(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px) rotate(-3deg); }
  20%, 40%, 60%, 80% { transform: translateX(5px) rotate(3deg); }
}

/* Level transition effect */
.level-transition {
  animation: levelUp 1s ease;
}

@keyframes levelUp {
  0% {
    transform: scale(1);
    filter: brightness(1);
  }
  50% {
    transform: scale(1.1);
    filter: brightness(1.3);
  }
  100% {
    transform: scale(1);
    filter: brightness(1);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .classic-bg {
    color: white;
  }
  
  .mountain-bg {
    color: white;
  }
}

/* Theme selection improvements */
.theme-card {
  position: relative;
  border-radius: 0.75rem;
  overflow: hidden;
  transition: all 0.3s ease;
  height: 150px;
  width: 100%;
}

.theme-classic {
  background: linear-gradient(180deg, #eff6ff 0%, #dbeafe 100%);
}

.theme-space {
  background: linear-gradient(185deg, #0f172a 0%, #1e1b4b 40%, #3b0764 80%, #701a75 100%);
}

.theme-ocean {
  background: linear-gradient(180deg, #0ea5e9 0%, #0284c7 30%, #036aa1 60%, #0c4a6e 100%);
}

.theme-mountain {
  background: linear-gradient(180deg, #93c5fd 0%, #bfdbfe 60%, #dbeafe 100%);
}

.theme-preview-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
}

.theme-title {
  position: absolute;
  bottom: 10px;
  left: 0;
  right: 0;
  text-align: center;
  font-weight: 600;
  padding: 0.5rem;
  border-radius: 0 0 0.75rem 0.75rem;
}

.theme-classic .theme-title {
  background-color: rgba(59, 130, 246, 0.8);
  color: white;
}

.theme-space .theme-title {
  background-color: rgba(139, 92, 246, 0.8);
  color: white;
}

.theme-ocean .theme-title {
  background-color: rgba(6, 182, 212, 0.8);
  color: white;
}

.theme-mountain .theme-title {
  background-color: rgba(99, 102, 241, 0.8);
  color: white;
}

.theme-selected {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5), 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transform: translateY(-5px);
} 
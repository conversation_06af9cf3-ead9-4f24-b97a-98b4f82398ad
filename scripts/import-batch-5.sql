SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'adj', 'catlico', 'Catholic');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'adj', 'artstico', 'artistic');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'adj', 'nervioso', 'nervous, uptight');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'adj', 'colombiano', 'Colombian');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'adj', 'gordo', 'fat');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'adj', 'listo', 'ready (after estar), clever, intelligent (after ser)');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'adj', 'gris', 'grey');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'adj', 'judo', 'Jewish');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'adj', 'chileno', 'Chilean');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'adj', 'dbil', 'weak');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'adj', 'deportivo', 'sporty, sports');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'adj', 'alegre', 'cheerful, happy, lively');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'adj', 'delgado', 'thin, slim');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'adj', 'feo', 'ugly');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'adj', 'tonto', 'silly');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'adj', 'musulmn', 'Muslim');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'adj', 'rubio', 'blond, fair');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'adj', 'simptico', 'nice, friendly');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'adj', 'casado', 'married');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'adj', 'gracioso', 'funny');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'adj', 'guapo', 'good-looking');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'adj', 'soltero', 'single, unmarried');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'adj', 'bi(sexual)', 'bi(sexual)');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'adj', 'enojado', 'angry');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'adj', 'hetero(sexual)', 'straight, heterosexual');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'adj', 'marrn', 'brown');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'adj', 'moreno', 'brown (hair), dark (skin)');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'adj', 'perezoso', 'lazy');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'adj', 'transgnero', 'transgender');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'adj', 'vegano', 'vegan');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'adj', 'vegetariano', 'vegetarian');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (f)', 'mujer', 'woman, wife');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (f)', 'madre', 'mother');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (f)', 'familia', 'family');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (f)', 'relacin', 'relationship');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (f)', 'escuela', '(primary) school');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (f)', 'nia', 'child, young girl');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (f)', 'compaa', 'company');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (f)', 'hija', 'daughter, child (f)');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (f)', 'abuela', 'grandmother');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (f)', 'pareja', 'couple, partner');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (f)', 'tradicin', 'tradition');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (f)', 'presin', 'pressure');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (f)', 'ta', 'aunt');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (f)', 'religin', 'religion');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (f)', 'confianza', 'confidence, trust');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (f)', 'amistad', 'friendship');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (f)', 'personalidad', 'personality, celebrity');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (f)', 'discusin', 'discussion, argument');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (f)', 'identidad', 'identity');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (f)', 'tarjeta', 'written card, bank card');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (f)', 'novia', 'girlfriend, bride');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (f)', 'boda', 'wedding');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (f)', 'hermana', 'sister');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (f)', 'madrastra', 'stepmother');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'animal', 'animal');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'nombre', 'name');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'hombre', 'man');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'hijo', 'son, child (m)');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'padre; padres', 'father; parents');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'ojo', 'eye');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'nio', 'child, little boy');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'amigo', 'friend');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'amor', 'love');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'hermano', 'brother');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'papel', 'paper, role, part');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'miembro', 'member');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'extranjero', 'abroad, foreigner (m)');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'recuerdo', 'memory, souvenir');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'carcter', 'personality, character');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'contacto', 'contact');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'apoyo', 'support, backing');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'telfono', 'phone, telephone');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'pelo', 'hair');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'perro', 'dog');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'caballo', 'horse');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'consejo', '(piece of) advice');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'sentimiento', 'feeling, sentiment');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'marido', 'husband');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'to', 'uncle');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'famoso', 'celebrity, famous person');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'respeto', 'respect, regard');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'matrimonio', 'marriage');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'novio', 'boyfriend, groom');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'primo', 'cousin');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'sexo', 'sex');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'comportamiento', 'behaviour');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'gato', 'cat');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'cuidado', 'care, carefulness');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'humor', 'humour, mood');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'regalo', 'present, gift');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'beb', 'baby');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'apellido', 'surname');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'cumpleaos', 'birthday');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'abuelo', 'grandfather');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'padrastro', 'stepfather');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'n (m)', 'tatuaje', 'tattoo');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'v', 'pasar', '(to) pass, spend (time), happen I  passing, spending (time), happening');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'v', 'parecer; parecerse a', '(to) seem I seeming; (to) look like | looking like');
SELECT import_gcse_vocabulary_simple('People and lifestyle', 'Identity and relationships', 'v', 'llamar; llamarse', '(to) call, name | calling, naming; (to) be called | being called');
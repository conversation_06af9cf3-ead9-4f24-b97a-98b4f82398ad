[![3OtOp7R.th.png](https://iili.io/3OtOp7R.th.png)](https://freeimage.host/i/3OtOp7R)
#  Review Gate for Cursor IDE ゲート

**Cursor**  would often drop the mic 🎤 way too early! I'd give it a complex task, it'd use maybe 5 of its ~25 available tool calls for that single "main request," then call it a day. Not only was that untapped AI power for that *single thought*, but making small follow-up tweaks meant starting a *new request*. Doing that too often, and my precious **~500 monthly requests** (you know the ones!) would burn up much faster than I liked :( 

**Presenting :The Review Gate – The "Turn Your 500 Cursor Requests into 2500!" Rule!**
(Okay, maybe not *always* a perfect 5x, but you get the damn idea! 😉)

I cooked up this Global Rule for our beloved Cursor IDE to transform my (and your!) AI from a quick sprinter into an endurance marathon runner for complex ideas, all within the lifecycle of a *single main request*. I've basically told Cursor: "Hold up, *we're* not done with this request until *I* say we're done." Before it dares to end the conversation, it *must* open a special terminal for my (and your!) final, iterative commands. This lets us guide it to a much deeper completion *without burning another one of those valuable main requests*.

If each main request can now handle the depth of what might have taken 5 separate (and shallow) requests before, we're effectively **supercharging those ~500 monthly requests to feel like 2500 in terms of iterative power!** It’s about making every single one count, HARD.

## ✨ Key Awesomeness (What I Packed In)

* **AI On MY Leash:** Makes the Cursor Agent wait for *my* (and your!) "go-ahead" via a terminal before it truly signs off on an *initial* request.
* **Multiply Your Request Power:** Make *one* main request do the work of many! Instead of 5 new prompts (and 5 dings on your ~500 request counter!), use the Review Gate for 5 (or more!) iterative sub-prompts *within that single request's lifecycle and tool call budget*.
* **Unlock Full Tool Call Potential:** I designed this to help us guide the AI to use more of its ~25 available tool calls for a *single complex idea* through those sub-prompts.
* **Auto-Magic Script:** The rule itself tells Cursor how to **create the needed Python helper script** (`final_review_gate.py`) right in your project if it's not there. I wanted zero fuss for setup!
* **Terminal Power-Ups:** You can feed sub-prompts directly into the script's terminal. Cursor *listens* to this, just like I observed it listens for errors.
* **Cursor Native Feel:** I built this to leverage the way Cursor already interacts with sub-terminals for running code and fixing errors.

## 🛠️ The Guts (How I Made It Work, Super Simple)

1.  **You (or I):** Give Cursor a task (this counts as 1 main request towards your ~500).
2.  **Cursor AI:** Does its main job (coding, analysis, maybe a few tool calls from the ~25 for this request).
3.  **Review Gate Kicks In (The Magic Part I Designed!):**
    * AI checks/creates the `final_review_gate.py` script (I put this logic in the rule!).
    * AI runs this script in a **sub-terminal within your Cursor chat area**.
    * AI announces it's waiting for your input *there*.
4.  **You (in the script's terminal):** Type quick follow-ups (e.g., "`Now add docstrings to all new functions.`") or type `TASK_COMPLETE`. Each of these is *not* a new main request.
5.  **Cursor AI (listening to the terminal, as instructed by my rule):**
    Reads your terminal input, acts on it (more coding, *more tool calls from the original budget*!), responds in the main chat, then waits for your *next* terminal input.
6.  **Loop!** This continues, deepening the work on your original request, until you type `TASK_COMPLETE` in the script's terminal or **CTRL + C**.

## 🚀 Get It Going (Installation)

It's a single, mighty rule I've put together. The Python script is **embedded inside it**.

1.  **Copy THE Rule:** You'll need the "Review Gate" rule text that we I perfected.
2.  **Cursor Settings:** Open your Cursor IDE settings.
3.  **Global AI Rules:** Find the section for "Rules".
4.  **Paste & Save:** Paste the entire rule. Save.
5.  **Boom!** My Review Gate is now armed to make every main request count for *way* more.

## 💡 Play Smart (My Tips & The "Why")

* **Why I built this hack:** To stop Cursor from ending too soon when I have iterative follow-ups for the *same original thought process*. It’s about turning a 5-tool-call answer into a 20-tool-call deep dive *if needed*, all while **making one of your ~500 monthly requests deliver the value of potentially five or more!**
* **How to use it:** Just give Cursor a normal, complex task. The rule automatically engages at the end of the AI's primary work. Then, use that script terminal for your fine-tuning.
* **Be Clear in Sub-Prompts:** I've found short, direct instructions in the script's terminal work best.
* **`TASK_COMPLETE` is Your Exit:** Don't forget to type this in the script's terminal to let the AI finally rest (and free up that main request slot in its mind).

## ⚠️ Heads Up! (My Friendly Warnings)

* **EXPERIMENTAL!** This is a power-user move I cooked up. It works because we're very cleverly instructing the AI.
* **AI WRITES A SCRIPT (As I Told It To!):** The rule instructs Cursor to create `final_review_gate.py` in your project root. **Understand the implications.** Ensure Cursor has permissions.
* **PYTHON NEEDED:** Cursor needs to be able to run `python3` or `python` commands for my script.
* **CURSOR UPDATES MIGHT CHANGE THINGS:** Future Cursor versions could affect how this rule behaves. What works today might need tweaks tomorrow!
* **IT'S ON YOU:** This is my custom rule, but you're the one using it. Use responsibly!

## 🧑‍💻 About Me & This Rule

This "Review Gate" rule was born from my own desire to truly partner with Cursor's AI and squeeze every ounce of value from my request quota. My goal? To make every interaction as deep and complete as possible—and ensure every available tool call for a big idea gets its chance to shine, making each of those ~500 requests count like gold! It was crafted through a detailed back-and-forth to make our Cursor Agent dance to a more precise and *efficient* tune.

To connect with me or learn more about my work, visit: [www.audienclature.com](https://www.audienclature.com)

---

🎁 **For a surprise, please look at the V2 directory!**

*Happy (and extended) coding with Cursor! May your AI always await your final command, your tool calls be plentifully used, and your monthly requests feel like they last forever!* ✨